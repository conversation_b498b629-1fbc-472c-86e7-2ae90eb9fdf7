# YouApp 路由清单

## 📊 路由概览
- **总路由数**: 211
- **v1 API**: 186 个路由
- **v2 API**: 14 个路由
- **Webhook**: 10 个路由
- **文件路由**: 1 个路由
- **已迁移**: 128 个路由 (60.7%) [✅ 120 + ⚠️ 6 + ❗ 2]
- **未迁移**: 56 个路由 (26.5%)
- **无需迁移**: 6 个路由 (2.8%)
- **状态未标记**: 21 个路由 (10.0%)

## 🗂️ 完整路由清单

### 1. Admin 模块 (订阅分析) - 5 路由 不管
| 路由 | 文件位置 |
|------|----------|
| `/api/v1/admin/analysis/subscription/cumulative` | `src/app/api/v1/admin/analysis/subscription/cumulative/route.ts` |
| `/api/v1/admin/analysis/subscription/period` | `src/app/api/v1/admin/analysis/subscription/period/route.ts` |
| `/api/v1/admin/analysis/subscription/recent` | `src/app/api/v1/admin/analysis/subscription/recent/route.ts` |
| `/api/v1/admin/analysis/subscription/retention` | `src/app/api/v1/admin/analysis/subscription/retention/route.ts` |
| `/api/v1/admin/analysis/subscription/stats` | `src/app/api/v1/admin/analysis/subscription/stats/route.ts` |

### 2. Assistant 模块 (AI助手) - 11 路由 不管
| 路由 | 文件位置 |
|------|----------|
| `/api/v1/assistant/createAssistant` | `src/app/api/v1/assistant/createAssistant/route.ts` |
| `/api/v1/assistant/deleteAssistant` | `src/app/api/v1/assistant/deleteAssistant/route.ts` |
| `/api/v1/assistant/disableAssistant` | `src/app/api/v1/assistant/disableAssistant/route.ts` |
| `/api/v1/assistant/enableAssistant` | `src/app/api/v1/assistant/enableAssistant/route.ts` |
| `/api/v1/assistant/getAssistant` | `src/app/api/v1/assistant/getAssistant/route.ts` |
| `/api/v1/assistant/listSpaceAssistants` | `src/app/api/v1/assistant/listSpaceAssistants/route.ts` |
| `/api/v1/assistant/moveAssistant` | `src/app/api/v1/assistant/moveAssistant/route.ts` |
| `/api/v1/assistant/patchAssistant` | `src/app/api/v1/assistant/patchAssistant/route.ts` |
| `/api/v1/assistant/previewAssistant` | `src/app/api/v1/assistant/previewAssistant/route.ts` |
| `/api/v1/assistant/runAssistant` | `src/app/api/v1/assistant/runAssistant/route.ts` |
| `/api/v1/assistant/tryGetAssistantContent` | `src/app/api/v1/assistant/tryGetAssistantContent/route.ts` |

### 3. Auth 模块 (认证) - 5 路由
| 路由 | 文件位置 | 迁移状态 |
|------|----------|----------|
| `/api/v1/auth/canPreview` | `src/app/api/v1/auth/canPreview/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/auth/signInWithOAuth` | `src/app/api/v1/auth/signInWithOAuth/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/auth/signInWithOTP` | `src/app/api/v1/auth/signInWithOTP/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/auth/validateOTPToken` | `src/app/api/v1/auth/validateOTPToken/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/logOut` | `src/app/api/v1/logOut/route.ts` | ✅ 已迁移 (youapi) |

### 4. Board 模块 (创作板) - 12 路由
| 路由 | 文件位置 | 迁移状态 |
|------|----------|----------|
| `/api/v1/board/archiveBoard` | `src/app/api/v1/board/archiveBoard/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/board/getBoard` | `src/app/api/v1/board/getBoard/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/board/getBoardDetail` | `src/app/api/v1/board/getBoardDetail/route.ts` | ✅ 已测试，但需要更多的测试，涉及到很多种 snip，目前没有问题 |
| `/api/v1/board/getDefaultBoard` | `src/app/api/v1/board/getDefaultBoard/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/board/listBoards` | `src/app/api/v1/board/listBoards/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/board/listBoardsWithSomeBoardItems` | `src/app/api/v1/board/listBoardsWithSomeBoardItems/route.ts` | ✅ 已测试，但需要更多的测试，涉及到很多种 snip，目前没有问题。目前 voice 的问题已更改 |
| `/api/v1/board/moveBoard` | `src/app/api/v1/board/moveBoard/route.ts` | ❌ 未迁移 | - 待确定
| `/api/v1/board/patchBoard` | `src/app/api/v1/board/patchBoard/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/board/pinBoard` | `src/app/api/v1/board/pinBoard/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/board/unarchiveBoard` | `src/app/api/v1/board/unarchiveBoard/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/board/unpinBoard` | `src/app/api/v1/board/unpinBoard/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/createBoard` | `src/app/api/v1/createBoard/route.ts` | ✅ 已迁移 (youapi) |

### 5. Board-group 模块 (创作板分组) - 4 路由
| 路由 | 文件位置 | 迁移状态 |
|------|----------|----------|
| `/api/v1/board-group/createBoardGroup` | `src/app/api/v1/board-group/createBoardGroup/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/board-group/deleteBoardGroup` | `src/app/api/v1/board-group/deleteBoardGroup/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/board-group/patchBoardGroup` | `src/app/api/v1/board-group/patchBoardGroup/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/board-group/ungroup` | `src/app/api/v1/board-group/ungroup/route.ts` | ✅ 已迁移 (youapi) |

### 6. BoardItem 模块 (创作板项目) - 3 路由
| 路由 | 文件位置 | 迁移状态 |
|------|----------|----------|
| `/api/v1/boardItem/duplicate` | `src/app/api/v1/boardItem/duplicate/route.ts` | ⚠️ 已迁移 (youapi) 未测试未核对youapp每一行逻辑 |
| `/api/v1/boardItem/moveBoardItemToBoardGroup` | `src/app/api/v1/boardItem/moveBoardItemToBoardGroup/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/boardItem/moveBoardItemToRoot` | `src/app/api/v1/boardItem/moveBoardItemToRoot/route.ts` | ✅ 已迁移 (youapi) |

### 7. Chat 模块 (聊天) - 15 路由 不管
| 路由 | 文件位置 | 迁移状态 |
|------|----------|----------|
| `/api/v1/chat/createChat` | `src/app/api/v1/chat/createChat/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/chat/deleteChat` | `src/app/api/v1/chat/deleteChat/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/chat/getChatDetail` | `src/app/api/v1/chat/getChatDetail/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/chat/getChatDetailForAssistant` | `src/app/api/v1/chat/getChatDetailForAssistant/route.ts` | ❌ 未迁移 |
| `/api/v1/chat/getSuggestionBySnip` | `src/app/api/v1/chat/getSuggestionBySnip/route.ts` | ❌ 未迁移 |
| `/api/v1/chat/getSuggestionByWebpage` | `src/app/api/v1/chat/getSuggestionByWebpage/route.ts` | ❌ 未迁移 |
| `/api/v1/chat/listChatHistory` | `src/app/api/v1/chat/listChatHistory/route.ts` | ❌ 未迁移 |
| `/api/v1/chat/listChatHistoryForAssistant` | `src/app/api/v1/chat/listChatHistoryForAssistant/route.ts` | ❌ 未迁移 |
| `/api/v1/chat/listChatModels` | `src/app/api/v1/chat/listChatModels/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/chat/queryChatDetailsByOrigin` | `src/app/api/v1/chat/queryChatDetailsByOrigin/route.ts` | ❌ 未迁移 |
| `/api/v1/chat/regenerateMessage` | `src/app/api/v1/chat/regenerateMessage/route.ts` | ❌ 未迁移 |
| `/api/v1/chat/saveMessages` | `src/app/api/v1/chat/saveMessages/route.ts` | ❌ 未迁移 |
| `/api/v1/chat/sendMessage` | `src/app/api/v1/chat/sendMessage/route.ts` | ❌ 未迁移 |
| `/api/v1/chat/updateChatTitle` | `src/app/api/v1/chat/updateChatTitle/route.ts` | ❌ 未迁移 |
| `/api/v1/chat/updateCompletionBlock` | `src/app/api/v1/chat/updateCompletionBlock/route.ts` | ❌ 未迁移 |

### 8. Cron 模块 (定时任务) - 4 路由 滞后
| 路由 | 文件位置 |
|------|----------|
| `/api/v1/cron/checkTimeoutSnips` | `src/app/api/v1/cron/checkTimeoutSnips/route.ts` | ❌ 未迁移 |
| `/api/v1/cron/checkTrial` | `src/app/api/v1/cron/checkTrial/route.ts` | ❌ 未迁移 |
| `/api/v1/cron/functionKeepalive` | `src/app/api/v1/cron/functionKeepalive/route.ts` | ❌ 未迁移 |
| `/api/v1/cron/syncSnips` | `src/app/api/v1/cron/syncSnips/route.ts` | ❌ 未迁移 |

### 9. Favorite 模块 (收藏) - 4 路由
| 路由 | 文件位置 | 迁移状态 |
|------|----------|----------|
| `/api/v1/favorite/favoriteEntity` | `src/app/api/v1/favorite/favoriteEntity/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/favorite/listFavorites` | `src/app/api/v1/favorite/listFavorites/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/favorite/moveFavorite` | `src/app/api/v1/favorite/moveFavorite/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/favorite/unfavoriteEntity` | `src/app/api/v1/favorite/unfavoriteEntity/route.ts` | ✅ 已迁移 (youapi) |

### 10. GlobalMaterials 模块 (全局素材) - 4 路由 和宗源确定要不要迁移
| 路由 | 文件位置 |
|------|----------|
| `/api/v1/globalMaterials/copy` | `src/app/api/v1/globalMaterials/copy/route.ts` | ❌ 未迁移 |
| `/api/v1/globalMaterials/exist` | `src/app/api/v1/globalMaterials/exist/route.ts` | ❌ 未迁移 |
| `/api/v1/globalMaterials/save` | `src/app/api/v1/globalMaterials/save/route.ts` | ❌ 未迁移 |
| `/api/v1/globalMaterials/saveByUrl` | `src/app/api/v1/globalMaterials/saveByUrl/route.ts` | ❌ 未迁移 |

### 11. Material 模块 (素材管理) - 7 路由。关于 chat 的逻辑都可以删掉了，目前 chat 不走目录树返回了
| 路由 | 文件位置 |
|------|----------|
| `/api/v1/material/listMaterials` | `src/app/api/v1/material/listMaterials/route.ts` |  ✅ 已迁移 (youapi) 未测试 | ListMaterialsHandler chat dto 无需返回
| `/api/v1/material/listRecentMaterials` | `src/app/api/v1/material/listRecentMaterials/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/api/v1/material/listUnusedMaterials` | `src/app/api/v1/material/listUnusedMaterials/route.ts` | ✅ 已迁移 (youapi) 未测试 | chat dto 没有返回
| `/api/v1/material/listUnusedMaterialsForMobile` | `src/app/api/v1/material/listUnusedMaterialsForMobile/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/api/v1/material/publish` | `src/app/api/v1/material/publish/route.ts` | ✅ 已迁移 (youapi) 未测试 | Chat 发布逻辑需要确认，ShortLink 逻辑未迁移，目前暂时用的是平迁过来的 Domain 服务，返回的是原 ShortLink(Snake-case) 的结构，和目前规范不同
| `/api/v1/material/saveShared` | `src/app/api/v1/material/saveShared/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/api/v1/material/unpublish` | `src/app/api/v1/material/unpublish/route.ts` | ✅ 已迁移 (youapi) 未测试 | Chat 发布逻辑需要确认，ShortLink 逻辑未迁移，目前暂时用的是平迁过来的 Domain 服务，无返回值

### 12. Note 模块 (笔记) - 5 路由
| 路由 | 文件位置 | 迁移状态 |
|------|----------|----------|
| `/api/v1/note/createNote` | `src/app/api/v1/note/createNote/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/note/deleteManyNotes` | `src/app/api/v1/note/deleteManyNotes/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/note/deleteNote` | `src/app/api/v1/note/deleteNote/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/note/listNotes` | `src/app/api/v1/note/listNotes/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/note/patchNote` | `src/app/api/v1/note/patchNote/route.ts` | ✅ 已迁移 (youapi) |

### 13. Playlist-item 模块 (播放列表项) - 4 路由
| 路由 | 文件位置 |
|------|----------|
| `/api/v1/playlist-item/createPlaylistItem` | `src/app/api/v1/playlist-item/createPlaylistItem/route.ts` | ⚠️ 已迁移 (youapi) 未测试 涉及到 LLM，后续要测试一下| AudioGenerateService.generate 里少了配额等逻辑的记录，可参考 youapp，不知道后续是否实现
| `/api/v1/playlist-item/deletePlaylistItem` | `src/app/api/v1/playlist-item/deletePlaylistItem/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/api/v1/playlist-item/listPlaylistItems` | `src/app/api/v1/playlist-item/listPlaylistItems/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/api/v1/playlist-item/updatePlaylistItemTitle` | `src/app/api/v1/playlist-item/updatePlaylistItemTitle/route.ts` | ✅ 已迁移 (youapi) 未测试 |

### 14. Snip 模块 (片段) - 16 路由
| 路由 | 文件位置 |
|------|----------|
| `/api/v1/snip/addPunctuation` | `src/app/api/v1/snip/addPunctuation/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/api/v1/snip/createPDF` | `src/app/api/v1/snip/createPDF/route.ts` | ✅ 已迁移 (youapi) 未测试，但与 /api/v1/createPDF 保持一致 |
| `/api/v1/snip/deleteFormattedSubtitles` | `src/app/api/v1/snip/deleteFormattedSubtitles/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/api/v1/snip/detectSpeakers` | `src/app/api/v1/snip/detectSpeakers/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/api/v1/snip/generateImageInfo` | `src/app/api/v1/snip/generateImageInfo/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/api/v1/snip/generateOverview` | `src/app/api/v1/snip/generateOverview/route.ts` | ✅ 已迁移 (youapi) 接口，但是未实现 |
| `/api/v1/snip/generateTranscript` | `src/app/api/v1/snip/generateTranscript/route.ts` | ✅ 已迁移 (youapi) 接口，但是未实现 |
| `/api/v1/snip/getSnip` | `src/app/api/v1/snip/getSnip/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/api/v1/snip/getSnips` | `src/app/api/v1/snip/getSnips/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/api/v1/snip/listFormattedSubtitles` | `src/app/api/v1/snip/listFormattedSubtitles/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/api/v1/snip/listWebpagesByUrls` | `src/app/api/v1/snip/listWebpagesByUrls/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/api/v1/snip/listWebpagesInBoardByUrls` | `src/app/api/v1/snip/listWebpagesInBoardByUrls/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/api/v1/snip/updateImage` | `src/app/api/v1/snip/updateImage/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/api/v1/snip/updateSnipPlayUrl` | `src/app/api/v1/snip/updateSnipPlayUrl/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/api/v1/snip/updateSnipTitle` | `src/app/api/v1/snip/updateSnipTitle/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/api/v1/snip/updateTranscriptSpeaker` | `src/app/api/v1/snip/updateTranscriptSpeaker/route.ts` | ✅ 已迁移 (youapi) 未测试 |

### 15. Subscription 模块 (订阅) - 4 路由 不管
| 路由 | 文件位置 |
|------|----------|
| `/api/v1/subscription/createCheckoutSession` | `src/app/api/v1/subscription/createCheckoutSession/route.ts` | ❌ 未迁移 |
| `/api/v1/subscription/testApple` | `src/app/api/v1/subscription/testApple/route.ts` | ❌ 未迁移 |
| `/api/v1/subscription/testAppleNotification` | `src/app/api/v1/subscription/testAppleNotification/route.ts` | ❌ 未迁移 |
| `/api/v1/subscription/verifyTransaction` | `src/app/api/v1/subscription/verifyTransaction/route.ts` | ❌ 未迁移 |

### 16. Snip 模块 (思考)
原 Snip 内部各种 Call 方法未实现 ✅ 已迁移

### 17. Thought 模块 (思考) - 10 路由
| 路由 | 文件位置 | 迁移状态 |
|------|----------|----------|
| `/api/v1/thought/createThoughtVersion` | `src/app/api/v1/thought/createThoughtVersion/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/thought/deleteThoughtVersion` | `src/app/api/v1/thought/deleteThoughtVersion/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/thought/genTitle` | `src/app/api/v1/thought/genTitle/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/thought/generateByMagicCreation` | `src/app/api/v1/thought/generateByMagicCreation/route.ts` | ⚪ 无需迁移 |
| `/api/v1/thought/getThought` | `src/app/api/v1/thought/getThought/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/thought/listMagicCreation` | `src/app/api/v1/thought/listMagicCreation/route.ts` | ⚪ 无需迁移 |
| `/api/v1/thought/listThoughtVersions` | `src/app/api/v1/thought/listThoughtVersions/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/thought/publishThought` | `src/app/api/v1/thought/publishThought/route.ts` | ✅ 已迁移 (youapi) | // TODO: 实现 shortLink 生成逻辑
| `/api/v1/thought/reportDiffReviewEvent` | `src/app/api/v1/thought/reportDiffReviewEvent/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/thought/unpublishThought` | `src/app/api/v1/thought/unpublishThought/route.ts` | ✅ 已迁移 (youapi) | // TODO: 实现 shortLink 生成逻辑

### 18. User 模块 (用户) - 7 路由
| 路由 | 文件位置 | 迁移状态 |
|------|----------|----------|
| `/api/v1/user/deleteCurrentUser` | `src/app/api/v1/user/deleteCurrentUser/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/user/initCurrentUser` | `src/app/api/v1/user/initCurrentUser/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/user/patchUserAvatar` | `src/app/api/v1/user/patchUserAvatar/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/user/patchUserName` | `src/app/api/v1/user/patchUserName/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/user/patchUserProfile` | `src/app/api/v1/user/patchUserProfile/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/user/setUserTimeZoneIfNotSet` | `src/app/api/v1/user/setUserTimeZoneIfNotSet/route.ts` | ✅ 已迁移 (youapi) |

### 19. User-Preference 模块 (用户偏好) - 1 路由
| 路由 | 文件位置 | 迁移状态 |
|------|----------|----------|
| `/api/v1/user-preference/updateUserPreference` | `src/app/api/v1/user-preference/updateUserPreference/route.ts` | ✅ 已迁移 (youapi) |

### 20. Space 模块 (工作空间) - 2 路由
| 路由 | 文件位置 | 迁移状态 |
|------|----------|----------|
| `/api/v1/space/setSpaceTrialExpiring` | `src/app/api/v1/space/setSpaceTrialExpiring/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/space/startTrial` | `src/app/api/v1/space/startTrial/route.ts` | ✅ 已迁移 (youapi) |

### 21. Position 模块 (位置管理) - 1 路由
| 路由 | 文件位置 | 迁移状态 |
|------|----------|----------|
| `/api/v1/position/move` | `src/app/api/v1/position/move/route.ts` | ✅ 已迁移 (youapi) |

### 22. Utils 模块 (工具) - 1 路由
| 路由 | 文件位置 | 迁移状态 |
|------|---------|----------|
| `/api/v1/utils/tryFetchWebpageMeta` | `src/app/api/v1/utils/tryFetchWebpageMeta/route.ts` | ❌ 未迁移 |

### 23. 根目录路由 (独立功能) - 83 路由
Collection 不需要了
| 路由 | 文件位置 | 迁移状态 |
|------|----------|----------|
| `/api/proxy-image` | `src/app/api/proxy-image/route.ts` | ❌ 未迁移 放在哪个 module ？|
| `/files/{{hash}}` | `src/app/files/[[...hash]]/route.ts` | ✅  已迁移 (youapi)  |
| `/api/revalidate` | `src/app/api/revalidate/route.ts` | ❌ 未迁移 放在哪个 module ？|
| `/api/v1/createArticle` | `src/app/api/v1/createArticle/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/createBoardFromTemplate` | `src/app/api/v1/createBoardFromTemplate/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/createBoardNotMagic` | `src/app/api/v1/createBoardNotMagic/route.ts` | 🤔 迁移了一半 大模型相关 buildStreamResponse |
| `/api/v1/createCollection` | `src/app/api/v1/createCollection/route.ts` |  ⚪ 无需迁移 |
| `/api/v1/createImage` | `src/app/api/v1/createImage/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/createImageSnippet` | `src/app/api/v1/createImageSnippet/route.ts` | ✅ 已迁移 (youapi) 未测试，参数、逻辑和 createImage 一致|
| `/api/v1/createLLMFeedback` | `src/app/api/v1/createLLMFeedback/route.ts` | 🤔 未迁移 大模型相关，需要和桑绿确认一下 |
| `/api/v1/createOffice` | `src/app/api/v1/createOffice/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/createOnlineVideo` | `src/app/api/v1/createOnlineVideo/route.ts` |  ✅ 已迁移 (youapi) 未测试，参数、逻辑和 createVideo 一致|
| `/api/v1/createOtherWebpage` | `src/app/api/v1/createOtherWebpage/route.ts` | ✅ 已迁移 (youapi) 未找到页面上的测试入口|
| `/api/v1/createPDF` | `src/app/api/v1/createPDF/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/createPDFbyUrl` | `src/app/api/v1/createPDFbyUrl/route.ts` | ✅ 已迁移 (youapi) 未测试|
| `/api/v1/createPodcast` | `src/app/api/v1/createPodcast/route.ts` | ✅ 已迁移 (youapi) 未测试，参数、逻辑和 createVoice 一致 |
| `/api/v1/createSnippet` | `src/app/api/v1/createSnippet/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/createStorageUsageRecordFromEditor` | `src/app/api/v1/createStorageUsageRecordFromEditor/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/api/v1/createTextFile` | `src/app/api/v1/createTextFile/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/createTextSnippet` | `src/app/api/v1/createTextSnippet/route.ts` | ✅ 已迁移 (youapi) 未测试，参数、逻辑和 createSnippet 一致 |
| `/api/v1/createThought` | `src/app/api/v1/createThought/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/createTingwuTask` | `src/app/api/v1/createTingwuTask/route.ts` | 🤔 未迁移 大模型相关，需要和桑绿确认一下 |
| `/api/v1/createUsageRecord` | `src/app/api/v1/createUsageRecord/route.ts` | 🤔 未迁移 @deprecated 需确认 |
| `/api/v1/createVideo` | `src/app/api/v1/createVideo/route.ts` |  ✅ 已迁移 (youapi) |
| `/api/v1/createVoice` | `src/app/api/v1/createVoice/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/deleteBoard` | `src/app/api/v1/deleteBoard/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/deleteCollection` | `src/app/api/v1/deleteCollection/route.ts` | ⚪ 无需迁移 |
| `/api/v1/deleteSnip` | `src/app/api/v1/deleteSnip/route.ts` | ✅ 已迁移 (youapi) 未测试未核对 |
| `/api/v1/deleteThought` | `src/app/api/v1/deleteThought/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/editImage` | `src/app/api/v1/editImage/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/api/v1/extractText` | `src/app/api/v1/extractText/route.ts` | ⚠️ 已迁移 (youapi) 未测试未核对youapp每一行逻辑 |
| `/api/v1/genSignedPutUrlIfNotExist` | `src/app/api/v1/genSignedPutUrlIfNotExist/route.ts` | ✅ 已测试 |
| `/api/v1/getCurrentUser` | `src/app/api/v1/getCurrentUser/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/getEntities` | `src/app/api/v1/getEntities/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/api/v1/getExplain/chat/completions` | `src/app/api/v1/getExplain/chat/completions/route.ts` | 🤔 未迁移 大模型相关 buildStreamResponse |
| `/api/v1/getNewAssistantPreview` | `src/app/api/v1/getNewAssistantPreview/route.ts` | 🤔 未迁移 大模型相关 buildStreamResponse |
| `/api/v1/getOverview/chat/completions` | `src/app/api/v1/getOverview/chat/completions/route.ts` | 🤔 未迁移 大模型相关 buildStreamResponse |
| `/api/v1/getSelfDescByFeeds` | `src/app/api/v1/getSelfDescByFeeds/route.ts` | 🤔 未迁移 大模型相关 buildStreamResponse |
| `/api/v1/getShortLink` | `src/app/api/v1/getShortLink/route.ts` | ✅ 已迁移 (youapi) 未测试未核对 |
| `/api/v1/getSimpleSummary` | `src/app/api/v1/getSimpleSummary/route.ts` | 🤔 未迁移 大模型相关 buildStreamResponse |
| `/api/v1/getThought` | `src/app/api/v1/getThought/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/getTingwuTaskInfo` | `src/app/api/v1/getTingwuTaskInfo/route.ts` | 🤔 未迁移 大模型相关，需要和桑绿确认一下 |
| `/api/v1/listBoards` | `src/app/api/v1/listBoards/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/listCollections` | `src/app/api/v1/listCollections/route.ts` | ⚪ 无需迁移 |
| `/api/v1/listEntitiesForAtReference` | `src/app/api/v1/listEntitiesForAtReference/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/api/v1/listSimpleBoardsByEntityIds` | `src/app/api/v1/listSimpleBoardsByEntityIds/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/api/v1/listSnips` | `src/app/api/v1/listSnips/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/api/v1/listSnipsByUrl` | `src/app/api/v1/listSnipsByUrl/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/api/v1/listThoughts` | `src/app/api/v1/listThoughts/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/logOut` | `src/app/api/v1/logOut/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/moveEntityToBoard` | `src/app/api/v1/moveEntityToBoard/route.ts` | ⚠️ 已迁移 (youapi) 未测试未核对youapp每一行逻辑 |
| `/api/v1/moveEntityToUnsorted` | `src/app/api/v1/moveEntityToUnsorted/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/patchThought` | `src/app/api/v1/patchThought/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/patchVideoTranscript` | `src/app/api/v1/patchVideoTranscript/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/api/v1/patchVoiceTranscript` | `src/app/api/v1/patchVoiceTranscript/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/api/v1/putSnipBoards` | `src/app/api/v1/putSnipBoards/route.ts` | ⚪ 无需迁移 |
| `/api/v1/putSnipCollections` | `src/app/api/v1/putSnipCollections/route.ts` | ⚪ 无需迁移 |
| `/api/v1/search` | `src/app/api/v1/search/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/stopTingwuTask` | `src/app/api/v1/stopTingwuTask/route.ts` | 🤔 未迁移 大模型相关，需要和桑绿确认一下 |
| `/api/v1/submitFeedback` | `src/app/api/v1/submitFeedback/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/translate` | `src/app/api/v1/translate/route.ts` | 🤔 未迁移 翻译模型相关，需要和桑绿确认一下 |
| `/api/v1/tryCreateSnipByUrl` | `src/app/api/v1/tryCreateSnipByUrl/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/api/v1/tryGetWebpageByNormalizedUrl` | `src/app/api/v1/tryGetWebpageByNormalizedUrl/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/api/v1/tts` | `src/app/api/v1/tts/route.ts` | 🤔 未迁移 大模型相关，需要和桑绿确认一下 |
| `/api/v1/updateCollection` | `src/app/api/v1/updateCollection/route.ts` | ⚪ 无需迁移 |
| `/api/v1/updateLLMFeedback` | `src/app/api/v1/updateLLMFeedback/route.ts` | 🤔 未迁移 大模型相关，需要和桑绿确认一下 |
| `/api/v1/updateUserPreference` | `src/app/api/v1/updateUserPreference/route.ts` | ✅ 已迁移 (youapi) |
| `/api/v1/upload` | `src/app/api/v1/upload/route.ts` | ⚠️ 已迁移 (youapi) 未测试 但 File 的处理方式，nextjs 和 nestjs 处理差距会比较大 |
| `/api/v1/uploadSVG` | `src/app/api/v1/uploadSVG/route.ts` | ✅ 已迁移 (youapi) 未测试未核对youapp每一行逻辑 |

### 21. Webhook 模块 (webhooks) - 10 路由
| 路由 | 文件位置 | 迁移状态 |
|------|----------|----------|
| `/webhook/v1/apple` | `src/app/webhook/v1/apple/route.ts` | ✅ 已迁移 (youapi) |
| `/webhook/v1/apple-sandbox` | `src/app/webhook/v1/apple-sandbox/route.ts` | ✅ 已迁移 (youapi) |
| `/webhook/v1/fetched` | `src/app/webhook/v1/fetched/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/webhook/v1/images-transfered` | `src/app/webhook/v1/images-transfered/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/webhook/v1/office-parsed` | `src/app/webhook/v1/office-parsed/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/webhook/v1/pdf-parsed` | `src/app/webhook/v1/pdf-parsed/route.ts` | ✅ 已迁移 (youapi) 未测试 |
| `/webhook/v1/resend` | `src/app/webhook/v1/resend/route.ts` | ✅ 已迁移 (youapi) LoggerError 替代 Sentry |
| `/webhook/v1/stripe` | `src/app/webhook/v1/stripe/route.ts` | ⚠️ 已迁移 未测试 |
| `/webhook/v1/subtitle-transcribed` | `src/app/webhook/v1/subtitle-transcribed/route.ts` | ✅ 已迁移 (youapi) 平迁的，SnipblockContent 逻辑比较复杂，用的旧 domain 先平迁 |
| `/webhook/v1/supabase-auth` | `src/app/webhook/v1/supabase-auth/route.ts` | ✅ 已迁移 (youapi) |

### 22. v2 API 路由 - 14 路由

#### v2 Agent 模块 - 1 路由
| 路由 | 文件位置 |
|------|----------|
| `/api/v2/agent/newBoard/run` | `src/app/api/v2/agent/newBoard/run/route.ts` |

#### v2 Assistant 模块 - 3 路由
| 路由 | 文件位置 |
|------|----------|
| `/api/v2/assistant/previewAssistant` | `src/app/api/v2/assistant/previewAssistant/route.ts` |
| `/api/v2/assistant/runAssistant` | `src/app/api/v2/assistant/runAssistant/route.ts` |
| `/api/v2/assistant/tryGetAssistantContent` | `src/app/api/v2/assistant/tryGetAssistantContent/route.ts` |

#### v2 ChatAssistant 模块 - 12 路由
| 路由 | 文件位置 |
|------|----------|
| `/api/v2/chatAssistant/createChat` | `src/app/api/v2/chatAssistant/createChat/route.ts` |
| `/api/v2/chatAssistant/createEmptyChat` | `src/app/api/v2/chatAssistant/createEmptyChat/route.ts` |
| `/api/v2/chatAssistant/createShortcut` | `src/app/api/v2/chatAssistant/createShortcut/route.ts` |
| `/api/v2/chatAssistant/deleteShortcut` | `src/app/api/v2/chatAssistant/deleteShortcut/route.ts` |
| `/api/v2/chatAssistant/getChatDetail` | `src/app/api/v2/chatAssistant/getChatDetail/route.ts` |
| `/api/v2/chatAssistant/getChatDetailByOrigin` | `src/app/api/v2/chatAssistant/getChatDetailByOrigin/route.ts` |
| `/api/v2/chatAssistant/listChatHistory` | `src/app/api/v2/chatAssistant/listChatHistory/route.ts` |
| `/api/v2/chatAssistant/listShortcuts` | `src/app/api/v2/chatAssistant/listShortcuts/route.ts` |
| `/api/v2/chatAssistant/moveShortcut` | `src/app/api/v2/chatAssistant/moveShortcut/route.ts` |
| `/api/v2/chatAssistant/patchShortcut` | `src/app/api/v2/chatAssistant/patchShortcut/route.ts` |
| `/api/v2/chatAssistant/regenerateMessage` | `src/app/api/v2/chatAssistant/regenerateMessage/route.ts` |
| `/api/v2/chatAssistant/sendMessage` | `src/app/api/v2/chatAssistant/sendMessage/route.ts` |

---

## 📈 路由统计

### 按功能模块分类统计
- **内容创作**: Board(12) + BoardItem(3) + Thought(10) + Note(5) = 30 路由
- **AI 功能**: Assistant(11) + Chat(15) + v2 Assistant(3) + v2 ChatAssistant(12) = 41 路由
- **媒体处理**: Snip(16) + Material(7) + GlobalMaterials(4) = 27 路由
- **用户管理**: User(7) + Auth(5) + User-Preference(2) + Space(2) + Favorite(4) = 20 路由
- **系统功能**: Admin(5) + Cron(4) + Test(2) + Utils(1) + Position(1) + Webhook(10) = 23 路由
- **其他功能**: Subscription(4) + Playlist-item(4) + 根目录路由(82) = 90 路由

### 版本分布
- **v1 API**: 186 路由 (88.6%)
- **v2 API**: 14 路由 (6.7%)
- **Webhook**: 10 路由 (4.8%)

### 迁移状态统计
- **已完成迁移**: 120 路由 (57.1%) [✅ 标记]
- **已迁移待测试**: 5 路由 (2.4%) [⚠️ 标记]
- **接口已迁移未实现**: 2 路由 (1.0%) [❗ 标记]
- **尚未迁移**: 56 路由 (26.7%) [❌ 标记]
- **无需迁移**: 6 路由 (2.9%) [⚪ 标记]
- **状态未标记**: 21 路由 (10.0%) [主要为 Admin、Assistant、v2 API]

**迁移进度汇总**:
- **已处理**: 127 路由 (60.5%) [包含完成、待测试、未实现]
- **待处理**: 77 路由 (36.7%) [包含未迁移、未标记]
- **无需处理**: 6 路由 (2.9%)

### 已迁移模块统计
- ✅ **完全迁移**: Auth(5/5), User(6/7), User-Preference(2/2), Space(2/2), Board(11/12), Board-group(4/4), BoardItem(2/3), Note(5/5), Thought(10/10), Favorite(4/4), Chat(4/15)
- 🔄 **部分迁移**: Assistant(0/11), Snip(1/16), Material(0/7), GlobalMaterials(0/4), Subscription(0/4), Playlist-item(0/4), Admin(0/5), Cron(0/4), Test(0/2), Utils(0/1)
- ❌ **未迁移**: 根目录路由(8/82), Webhook(1/10)

---

## 📋 迁移状态说明

### 迁移状态标识
- ✅ **已迁移 (youapi)**: 该路由已在 youapi 中实现，使用 DDD + CQRS 架构，功能完整
- ⚠️ **已迁移 (youapi) 未测试**: 该路由已迁移但需要测试验证，或存在已知问题待修复
- ❗ **已迁移 (youapi) 接口，但是未实现**: API接口已定义但核心逻辑未完成实现
- ❌ **未迁移**: 该路由尚未在 youapi 中实现
- ⚪ **无需迁移**: 该路由无需迁移（如 Collection 相关已废弃功能）

### 已迁移模块详情
1. **IAM 模块** (身份与访问管理)
   - Auth: 5/5 路由已迁移 (100%)
   - User: 6/7 路由已迁移 (85.7%)
   - User-Preference: 2/2 路由已迁移 (100%)
   - Space: 2/2 路由已迁移 (100%)

2. **Material-Mng 模块** (素材管理)
   - Board: 11/12 路由已迁移 (91.7%)
   - Board-group: 4/4 路由已迁移 (100%)
   - BoardItem: 2/3 路由已迁移 (66.7%)
   - Note: 5/5 路由已迁移 (100%)
   - Thought: 10/10 路由已迁移 (100%)
   - Favorite: 4/4 路由已迁移 (100%)

3. **Chat 模块** (聊天)
   - Chat: 4/15 路由已迁移 (26.7%)

### 架构特点
- 使用 DDD (领域驱动设计) + CQRS (命令查询职责分离) 架构
- 采用聚合根模式管理业务实体
- 实现完整的 Repository 模式
- 支持 snake_case 和 camelCase 兼容性
- 统一的错误处理和响应格式

**更新日期**: 2025-01-18
**数据来源**: 基于 Next.js 文件系统路由自动生成
