// Migrated from Next.js to NestJS - Domain Service converted to Provider
import { Injectable, Logger } from '@nestjs/common';
import { EventBus } from '@nestjs/cqrs';
import { type ContentHandler, LanguageEnum } from '@repo/common';
import { EmailService } from '@/infra/plunk';
import { InvalidUser } from '../../common/errors';
import type { UserVO } from '../../common/types';
import type { PagingParam, PagingResponse } from '../../common/types/api.types';
import {
  AILanguageEnum,
  DisplayLanguageEnum,
  resolveLanguage,
  // resolveLanguage,
} from '../../common/types/system.types';
import { UserOnboardStatusEnum } from '../../common/types/user.types';
import { UserDAO, UserPreferenceDAO } from '../../dao/user';
import { UpdateAppMetaDataParam, UserAppMetadata, UserDO } from '../../dao/user/types';
import { UserDeletedEvent, UserPreferenceUpdatedEvent } from '../events';
import { SpaceDomainService } from '../space';
import type { SpaceStatusEnum } from '../space/types';
import { type SubscriptionStatusEnum } from '../subscription/types';
import { type PatchUserPreferenceParam, PatchUserPreferenceParamSchema } from './types';

export interface ListUserParam extends PagingParam {
  keyword?: string;
  onboardStatus?: UserOnboardStatusEnum;
  status?: SpaceStatusEnum | SpaceStatusEnum[];
  subscriptionStatus?: SubscriptionStatusEnum | SubscriptionStatusEnum[];
  profileCompleteness?: string;
  willingness?: string;
  hasMessage?: string;
  timeZone?: string;
  timeZoneCategory?: string | string[];
  cancelAtPeriodEnd?: string | string[];
}

@Injectable()
export class UserDomainService {
  private readonly logger = new Logger(UserDomainService.name);

  constructor(
    private readonly eventBus: EventBus,
    private readonly userDAO: UserDAO,
    private readonly userPreferenceDAO: UserPreferenceDAO,
    private readonly spaceDomainService: SpaceDomainService,
    private readonly emailService: EmailService,
  ) {}

  private toUserVO(user: UserDO | null): UserVO | null {
    if (!user) return null;

    return {
      id: user.id,
      email: user.email!,
      name: user.raw_app_meta_data?.name || user.raw_user_meta_data?.name,
      picture: user.raw_app_meta_data?.avatar_url || user.raw_user_meta_data?.picture,
      created_at: user.created_at as Date,
      updated_at: user.updated_at as Date,
      onboard_status: user.raw_app_meta_data?.onboard_status,
      profile: user.raw_app_meta_data?.profile,
      time_zone: user.raw_app_meta_data?.time_zone,
      confirmed_at: user.confirmed_at as Date,
      last_sign_in_at: user.last_sign_in_at as Date,
    };
  }

  async selectOneById(id: string): Promise<UserVO | null> {
    const user = await this.userDAO.selectOneById(id);
    return this.toUserVO(user);
  }

  async selectManyByIds(ids: string[]): Promise<UserVO[]> {
    const users = await this.userDAO.selectManyByIds(ids);
    return users.map(this.toUserVO) as UserVO[];
  }

  async list(param: ListUserParam): Promise<PagingResponse<UserVO>> {
    const result = await this.userDAO.list(param, {
      keyword: param.keyword,
      onboard_status: param.onboardStatus,
    });
    return {
      ...result,
      data: result.data.map(this.toUserVO) as UserVO[],
    };
  }

  async listWithSpace(param: ListUserParam) {
    const result = await this.userDAO.selectWithSpace(param, {
      keyword: param.keyword,
      onboard_status: param.onboardStatus,
      status: param.status,
      subscription_status: param.subscriptionStatus,
      profile_completeness: param.profileCompleteness,
      willingness: param.willingness,
      has_message: param.hasMessage,
      time_zone_category: param.timeZoneCategory,
    });
    return {
      ...result,
      data: result.data.map((item) => ({
        id: item.users.id,
        email: item.users.email!,
        name:
          (item.users.raw_app_meta_data as UserAppMetadata)?.name ||
          (item.users.raw_user_meta_data as any)?.name,
        picture: (item.users.raw_user_meta_data as any)?.picture,
        created_at: item.users.created_at as Date,
        updated_at: item.users.updated_at as Date,
        onboard_status:
          (item.users.raw_app_meta_data as UserAppMetadata)?.onboard_status ||
          UserOnboardStatusEnum.UNKNOWN,
        profile: (item.users.raw_app_meta_data as UserAppMetadata)?.profile,
        time_zone: (item.users.raw_app_meta_data as UserAppMetadata)?.time_zone,
        space: item.spaces ? this.spaceDomainService.do2entity(item.spaces as any) : undefined,
        confirmed_at: item.users.confirmed_at as Date,
        last_sign_in_at: item.users.last_sign_in_at as Date,
      })),
    };
  }

  private async updateAppMetaData(param: UpdateAppMetaDataParam): Promise<void> {
    const { id, raw_app_meta_data } = param;
    const user = await this.userDAO.selectOneById(id);
    if (!user) throw new InvalidUser();
    await this.userDAO.updateAppMetadata({
      id,
      raw_app_meta_data: {
        ...(user.raw_app_meta_data || {}),
        ...(raw_app_meta_data as object),
      },
    });
  }

  async patchUserName(param: { id: string; name: string }) {
    const { id, name } = param;
    await this.updateAppMetaData({
      id,
      raw_app_meta_data: {
        name,
      },
    });
    return await this.selectOneById(id);
  }

  async setUserTimeZone(param: { id: string; time_zone: string }) {
    const { id, time_zone } = param;
    await this.updateAppMetaData({
      id,
      raw_app_meta_data: {
        time_zone,
      },
    });
    return await this.selectOneById(id);
  }

  async patchUserAvatar(param: { id: string; avatar_url: string }) {
    const { id, avatar_url } = param;
    await this.updateAppMetaData({
      id,
      raw_app_meta_data: {
        avatar_url,
      },
    });
    return await this.selectOneById(id);
  }

  async patchUserProfile(param: { id: string; profile: object }) {
    const { id, profile } = param;
    await this.updateAppMetaData({
      id,
      raw_app_meta_data: {
        profile: profile || {},
      },
    });
    return await this.selectOneById(id);
  }

  async addUserToWaitlist(id: string) {
    await this.updateAppMetaData({
      id,
      raw_app_meta_data: {
        onboard_status: UserOnboardStatusEnum.WAITING,
      },
    });
    return await this.selectOneById(id);
  }

  async setUserOnboard(id: string) {
    await this.updateAppMetaData({
      id,
      raw_app_meta_data: {
        onboard_status: UserOnboardStatusEnum.ONBOARDED,
      },
    });

    const user = await this.selectOneById(id);

    return user;
  }

  async holdUser(id: string) {
    await this.updateAppMetaData({
      id,
      raw_app_meta_data: {
        onboard_status: UserOnboardStatusEnum.HOLD,
      },
    });
    return await this.selectOneById(id);
  }

  async getUserPreference(id: string) {
    const result = await this.userPreferenceDAO.selectOneByUserId(id);
    return result;
  }

  async createUserPreference(id: string) {
    return await this.userPreferenceDAO.upsertByUserId(id, {
      display_language: DisplayLanguageEnum['en-US'],
      ai_response_language: AILanguageEnum.system,
      ai_2nd_response_language: AILanguageEnum.system,
      enable_bilingual: true,
    });
  }

  async patchUserPreference(param: PatchUserPreferenceParam) {
    const { id, ...rest } = PatchUserPreferenceParamSchema.parse(param);
    // 确保用户偏好设置存在
    await this.getUserPreference(id as string);

    await this.userPreferenceDAO.upsertByUserId(id as string, {
      ...rest,
      updated_at: new Date(),
    });

    // 发布事件
    try {
      const user = await this.selectOneById(id as string);
      if (user?.email) {
        this.eventBus.publish(
          new UserPreferenceUpdatedEvent({
            email: user.email,
            preferences: rest,
            userId: id as string,
          }),
        );
      }
    } catch (error) {
      this.logger.error(error, 'Failed to publish UserPreferenceUpdatedEvent');
    }

    return await this.getUserPreference(id as string);
  }

  async getFullLLMResponseLanguage(param: {
    user_id: string;
    content: ContentHandler | null;
    locale?: string; // TODO: Pass locale explicitly instead of using cookies
  }) {
    const { user_id, content, locale } = param;
    const userPreference = await this.getUserPreference(user_id);
    if (!userPreference) {
      return resolveLanguage({
        language: AILanguageEnum.system,
        content,
        locale: (locale as LanguageEnum) || LanguageEnum['en-US'],
      });
    }

    const { ai_response_language, ai_2nd_response_language, enable_bilingual } = userPreference;

    return {
      primary: ai_response_language,
      secondary: enable_bilingual ? ai_2nd_response_language : null,
    };
  }

  public async getPrimaryResponseLanguage(param: { user_id: string; content?: ContentHandler }) {
    const { user_id, content } = param;
    const userPreference = await this.getUserPreference(user_id);
    if (!userPreference) {
      return resolveLanguage({
        language: AILanguageEnum.system,
        content: content || null,
        locale: LanguageEnum['en-US'],
      });
    }

    const is_language_code = (language: string) => {
      return Object.values(LanguageEnum).includes(language as LanguageEnum);
    };

    const { ai_response_language } = userPreference;
    if (ai_response_language === AILanguageEnum.system) {
      return resolveLanguage({
        language: AILanguageEnum.system,
        content: content || null,
        locale: LanguageEnum['en-US'],
      });
    } else if (ai_response_language === AILanguageEnum['follow-content']) {
      return resolveLanguage({
        language: AILanguageEnum['follow-content'],
        content: content || null,
        locale: LanguageEnum['en-US'],
      });
    } else if (is_language_code(ai_response_language)) {
      return ai_response_language;
    } else {
      return resolveLanguage({
        language: AILanguageEnum.system,
        content: content || null,
        locale: LanguageEnum['en-US'],
      });
    }
  }

  public async sendWaitlistEmail(param: { userId: string }) {
    const { userId } = param;
    const user = await this.selectOneById(userId);
    if (!user?.email) {
      this.logger.warn(`User ${userId} not found or has no email`);
      return;
    }

    try {
      // TODO: Implement proper email template when components are available
      await this.emailService.send({
        email: user.email,
        subject: 'Welcome to YouMind Waitlist',
        content: `Welcome to YouMind! User ID: ${userId}`, // Temporary plain text content
      });
      this.logger.log(`Waitlist email sent to ${user.email}`);
    } catch (error) {
      this.logger.error(error, `Failed to send waitlist email to ${user.email}`);
    }
  }

  async getUserCount() {
    const subscribedCount = await this.userDAO.countBySubscriptionStatus();
    return subscribedCount;
  }

  async deleteUser(user_id: string) {
    try {
      const user = await this.selectOneById(user_id);
      if (!user) {
        this.logger.warn(`User ${user_id} not found for deletion`);
        return;
      }

      // TODO: Implement proper user deletion when DAO method is available
      // await this.userDAO.deleteUser(user_id);
      this.logger.warn(`User deletion not yet implemented for ${user_id}`);

      // Publish deletion event
      if (user.email) {
        this.eventBus.publish(
          new UserDeletedEvent({
            userId: user_id,
            email: user.email,
          }),
        );
      }
    } catch (error) {
      this.logger.error(error, `Failed to delete user ${user_id}`);
      throw error;
    }
  }

  private async sendWelcomeEmail(param: { userId: string }) {
    const { userId } = param;
    const user = await this.selectOneById(userId);
    if (!user?.email) {
      this.logger.warn(`User ${userId} not found or has no email`);
      return;
    }

    try {
      // TODO: Implement proper email template when components are available
      await this.emailService.send({
        email: user.email,
        subject: 'Welcome to YouMind',
        content: `Welcome to YouMind! User ID: ${userId}`, // Temporary plain text content
      });
      this.logger.log(`Welcome email sent to ${user.email}`);
    } catch (error) {
      this.logger.error(error, `Failed to send welcome email to ${user.email}`);
    }
  }
}
