/**
 * Domain Module - 领域服务模块
 * 导出所有领域服务供应用层使用
 */

import { forwardRef, Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';

import { DaoModule } from '@/dao/dao.module';
import { InfraModule } from '@/infra/infra.module';
import { AIAskPromptService, WriterPromptService } from '@/infra/youllm/prompt';
import { BasePromptService } from '@/infra/youllm/prompt/base-prompt.service';
import { YoutubeFactoryService } from '@/modules/ai/tools/youtube-factory.tool';
// Import all domain services
import { BlockDomainService } from './block';
import { BoardDomainService } from './board';
import { BoardGroupDomainService } from './board-group';
import { BoardItemDomainService } from './board-item';
import { ChatDomainService } from './chat';
import { NewBoardWorkflowService } from './chat/agent/new_board';
// Import chat services
import { AskAIService } from './chat/ask_ai';
import { ChatAskService } from './chat/ask_ai/ask';
import { AssistantChatDomainService } from './chat/assistant';
import { BoardLLMDomainService } from './chat/board';
// Import context builders
import { ContextBuilder } from './chat/context';
import { LLMMessageAnalyzer } from './chat/context/analyzer';
import { DefaultContentRetriever } from './chat/context/retriever';
import { ChatQueryDomainService } from './chat/query';
import { ShortcutDomainService } from './chat/shortcut';
// Import tool services
import {
  AudioGenerateService,
  BoardSearchService,
  CreateBoardService,
  CreateSnipByUrlService,
  DiagramGenerateService,
  EditThoughtService,
  GoogleSearchService,
  ImageGenerateService,
  LibrarySearchService,
  OrganizeDirectoryStructureService,
  ToolCallService,
} from './chat/tool_call';
import { ChatV2DomainService } from './chat/v2';
import { ContentDomainService } from './content';
import { FavoriteDomainService } from './favorite';
import { FileDomainService } from './file';
import { GlobalMaterialsLibraryDomainService } from './global-materials-library';
import { YouLLMService } from './llm';
import { ContextManager } from './llm/context';
import { BoardContextBuilder, BoardGroupContextBuilder } from './llm/context/board';
import { ChatContextBuilder } from './llm/context/chat';
import { SnipContextBuilder } from './llm/context/snip';
import { ThoughtContextBuilder } from './llm/context/thought';
import { WebpageContextBuilder } from './llm/context/webpage';
import { NoteDomainService } from './note';
import { PlaylistItemDomainService } from './playlist-item';
import { SearchDomainService } from './search';
import { InternetSearchDomainService } from './search/internet';
import { SearchQueryService } from './search/query';
import { SearchResultDomainService } from './search/result';
import { ShortLinkDomainService } from './short-link';
import { SnipDomainService } from './snip';
import { SpaceDomainService } from './space';
import { SubscriptionDomainService } from './subscription';
import { SystemConfigDomainService } from './system-config';
import { ThoughtDomainService } from './thought';
import { UsageRecordDomainService } from './usage-record';
import {
  StorageUsageRecordCreatedHandler,
  TTSUsageRecordCreatedHandler,
} from './usage-record/handlers/usage-record-events.handler';
import { UserDomainService } from './user';
import {
  UserDeletedHandler,
  UserPreferenceUpdatedHandler,
} from './user/handlers/user-events.handler';
import { WebSearchDomainService } from './web-search';
import { ExternalSearchDomainService } from './web-search/external-search';
import { InternalSearchDomainService } from './web-search/internal-search';

const domainServices = [
  // Basic services (no cross-dependencies)
  ContentDomainService,
  UserDomainService,
  SpaceDomainService,
  // Core domain services (ordered by dependencies)
  BlockDomainService,
  BoardGroupDomainService,
  BoardDomainService,
  BoardItemDomainService,
  ChatDomainService,
  ChatV2DomainService,
  BoardLLMDomainService,
  ShortcutDomainService,
  FavoriteDomainService,
  FileDomainService,
  GlobalMaterialsLibraryDomainService,
  YouLLMService,
  NoteDomainService,
  InternetSearchDomainService,
  PlaylistItemDomainService,
  SearchDomainService,
  SearchQueryService,
  SearchResultDomainService,
  ShortLinkDomainService,
  SnipDomainService,
  SubscriptionDomainService,
  SystemConfigDomainService,
  ThoughtDomainService,
  UsageRecordDomainService,
  WebSearchDomainService,
  InternalSearchDomainService,
  ExternalSearchDomainService,
  BasePromptService,
  AIAskPromptService,
  WriterPromptService,
  ToolCallService,
];

const contextBuilders = [
  ContextBuilder,
  LLMMessageAnalyzer,
  DefaultContentRetriever,
  BoardContextBuilder,
  BoardGroupContextBuilder,
  ChatContextBuilder,
  SnipContextBuilder,
  ThoughtContextBuilder,
  WebpageContextBuilder,
  ContextManager,
];

const chatServices = [
  AskAIService,
  AssistantChatDomainService,
  ChatAskService,
  NewBoardWorkflowService,
  ChatQueryDomainService,
];

const toolServices = [
  LibrarySearchService,
  GoogleSearchService,
  CreateBoardService,
  BoardSearchService,
  OrganizeDirectoryStructureService,
  EditThoughtService,
  CreateSnipByUrlService,
  YoutubeFactoryService,
  ImageGenerateService,
  AudioGenerateService,
  DiagramGenerateService,
];

const eventHandlers = [
  UserPreferenceUpdatedHandler,
  UserDeletedHandler,
  StorageUsageRecordCreatedHandler,
  TTSUsageRecordCreatedHandler,
];

@Module({
  imports: [CqrsModule, DaoModule, forwardRef(() => InfraModule)],
  providers: [
    ...domainServices,
    ...contextBuilders,
    ...eventHandlers,
    ...chatServices,
    ...toolServices,
  ],
  exports: [...domainServices, ...contextBuilders, ...chatServices, ...toolServices],
})
export class DomainModule {}
