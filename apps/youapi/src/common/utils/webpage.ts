/**
 * Webpage parsing utility functions
 * Migrated from youapp/src/lib/utils
 */

/**
 * Parse webpage metadata from HTML content
 * @param url - The URL of the webpage
 * @param html - The HTML content to parse
 * @param options - Optional configuration for additional metadata extraction
 * @returns Parsed webpage metadata
 */
export function parseWebpageMetaFromHtml(
  url: string,
  html: string,
  options?: {
    extractExtendedMeta?: boolean; // Extract additional metadata like author, publish time, etc.
  },
): {
  url: string;
  normalized_url: string;
  title: string;
  description: string;
  site: {
    name: string;
    host: string;
    favicon_url: string;
  };
  // Extended metadata (only included when extractExtendedMeta is true)
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
} {
  try {
    const urlObj = new URL(url);

    // Extract title
    const titleMatch = html.match(/<title[^>]*>([^<]*)<\/title>/i);
    let title = titleMatch ? titleMatch[1].trim() : '';

    // Extract og:title (for extended metadata or fallback)
    const ogTitleMatch = html.match(
      /<meta[^>]*property\s*=\s*["']og:title["'][^>]*content\s*=\s*["']([^"']*)["']/i,
    );
    const ogTitle = ogTitleMatch ? ogTitleMatch[1].trim() : '';

    // If no title found, try og:title
    if (!title) {
      title = ogTitle;
    }

    // Fallback to hostname
    if (!title) {
      title = urlObj.hostname;
    }

    // Extract description
    let description = '';
    const descMatch = html.match(
      /<meta[^>]*name\s*=\s*["']description["'][^>]*content\s*=\s*["']([^"']*)["']/i,
    );
    if (descMatch) {
      description = descMatch[1].trim();
    } else {
      // Try og:description
      const ogDescMatch = html.match(
        /<meta[^>]*property\s*=\s*["']og:description["'][^>]*content\s*=\s*["']([^"']*)["']/i,
      );
      description = ogDescMatch ? ogDescMatch[1].trim() : '';
    }

    // Extract og:description for extended metadata
    const ogDescriptionMatch = html.match(
      /<meta[^>]*property\s*=\s*["']og:description["'][^>]*content\s*=\s*["']([^"']*)["']/i,
    );
    const ogDescription = ogDescriptionMatch ? ogDescriptionMatch[1].trim() : '';

    // Extract site name
    let siteName = '';
    const siteNameMatch = html.match(
      /<meta[^>]*property\s*=\s*["']og:site_name["'][^>]*content\s*=\s*["']([^"']*)["']/i,
    );
    if (siteNameMatch) {
      siteName = siteNameMatch[1].trim();
    } else {
      siteName = urlObj.hostname;
    }

    // Extract favicon
    let faviconUrl = '';
    const faviconMatch = html.match(
      /<link[^>]*rel\s*=\s*["'](?:icon|shortcut icon)["'][^>]*href\s*=\s*["']([^"']*)["']/i,
    );
    if (faviconMatch) {
      const href = faviconMatch[1].trim();
      if (href.startsWith('http')) {
        faviconUrl = href;
      } else if (href.startsWith('//')) {
        faviconUrl = urlObj.protocol + href;
      } else if (href.startsWith('/')) {
        faviconUrl = urlObj.origin + href;
      } else {
        faviconUrl = `${urlObj.origin}/${href}`;
      }
    } else {
      // Default favicon location
      faviconUrl = `${urlObj.origin}/favicon.ico`;
    }

    // Extract extended metadata if requested
    let extendedMeta = {};
    if (options?.extractExtendedMeta) {
      // Extract og:image
      const ogImageMatch = html.match(
        /<meta[^>]*property\s*=\s*["']og:image["'][^>]*content\s*=\s*["']([^"']*)["']/i,
      );
      let ogImage = ogImageMatch ? ogImageMatch[1].trim() : '';

      // Handle relative URLs for og:image
      if (ogImage && !ogImage.startsWith('http')) {
        if (ogImage.startsWith('//')) {
          ogImage = urlObj.protocol + ogImage;
        } else if (ogImage.startsWith('/')) {
          ogImage = urlObj.origin + ogImage;
        } else {
          ogImage = `${urlObj.origin}/${ogImage}`;
        }
      }

      // Extract author
      const authorMatch =
        html.match(/<meta[^>]*name\s*=\s*["']author["'][^>]*content\s*=\s*["']([^"']*)["']/i) ||
        html.match(
          /<meta[^>]*property\s*=\s*["']article:author["'][^>]*content\s*=\s*["']([^"']*)["']/i,
        );
      const author = authorMatch ? authorMatch[1].trim() : '';

      // Extract published time
      const publishedTimeMatch =
        html.match(
          /<meta[^>]*property\s*=\s*["']article:published_time["'][^>]*content\s*=\s*["']([^"']*)["']/i,
        ) ||
        html.match(/<meta[^>]*name\s*=\s*["']publish_date["'][^>]*content\s*=\s*["']([^"']*)["']/i);
      const publishedTime = publishedTimeMatch ? publishedTimeMatch[1].trim() : '';

      // Extract modified time
      const modifiedTimeMatch =
        html.match(
          /<meta[^>]*property\s*=\s*["']article:modified_time["'][^>]*content\s*=\s*["']([^"']*)["']/i,
        ) ||
        html.match(
          /<meta[^>]*name\s*=\s*["']last-modified["'][^>]*content\s*=\s*["']([^"']*)["']/i,
        );
      const modifiedTime = modifiedTimeMatch ? modifiedTimeMatch[1].trim() : '';

      extendedMeta = {
        ogTitle: ogTitle || undefined,
        ogDescription: ogDescription || undefined,
        ogImage: ogImage || undefined,
        author: author || undefined,
        publishedTime: publishedTime || undefined,
        modifiedTime: modifiedTime || undefined,
      };
    }

    return {
      url,
      normalized_url: url, // Will be normalized by the caller
      title: title || urlObj.hostname,
      description: description || '',
      site: {
        name: siteName || urlObj.hostname,
        host: urlObj.hostname,
        favicon_url: faviconUrl,
      },
      ...extendedMeta,
    };
  } catch (error) {
    console.warn('Error parsing webpage metadata:', error);

    // Fallback metadata
    const urlObj = new URL(url);
    const fallbackMeta = {
      url,
      normalized_url: url,
      title: urlObj.hostname,
      description: '',
      site: {
        name: urlObj.hostname,
        host: urlObj.hostname,
        favicon_url: `${urlObj.origin}/favicon.ico`,
      },
    };

    // Add empty extended metadata if requested
    if (options?.extractExtendedMeta) {
      return {
        ...fallbackMeta,
        ogTitle: undefined,
        ogDescription: undefined,
        ogImage: undefined,
        author: undefined,
        publishedTime: undefined,
        modifiedTime: undefined,
      };
    }

    return fallbackMeta;
  }
}
