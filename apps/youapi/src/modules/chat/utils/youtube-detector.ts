/**
 * 检测文本中是否包含 YouTube 链接
 * 支持的格式：
 * - https://www.youtube.com/watch?v=VIDEO_ID
 * - https://www.youtube.com/live/VIDEO_ID
 * - https://youtu.be/VIDEO_ID
 * - https://m.youtube.com/watch?v=VIDEO_ID
 */
export function detectYouTubeUrl(text: string): string | null {
  // YouTube URL 的正则表达式
  const youtubePatterns = [
    /(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]+)/,
    /(?:https?:\/\/)?(?:www\.)?youtube\.com\/live\/([a-zA-Z0-9_-]+)/,
    /(?:https?:\/\/)?youtu\.be\/([a-zA-Z0-9_-]+)/,
    /(?:https?:\/\/)?m\.youtube\.com\/watch\?v=([a-zA-Z0-9_-]+)/,
    /(?:https?:\/\/)?(?:www\.)?youtube\.com\/embed\/([a-zA-Z0-9_-]+)/,
  ];

  for (const pattern of youtubePatterns) {
    const match = text.match(pattern);
    if (match) {
      // 返回完整的 URL
      return match[0];
    }
  }

  return null;
}

/**
 * 从文本中提取 YouTube 视频 ID
 */
export function extractYouTubeVideoId(url: string): string | null {
  try {
    const parsedUrl = new URL(url);

    // 处理 youtu.be 短链接
    if (parsedUrl.hostname === 'youtu.be') {
      return parsedUrl.pathname.slice(1); // 移除开头的 '/'
    }

    // 处理 youtube.com 域名下的各种格式
    if (parsedUrl.hostname.includes('youtube.com')) {
      // 处理直播链接: /live/VIDEO_ID
      if (parsedUrl.pathname.startsWith('/live/')) {
        return parsedUrl.pathname.replace('/live/', '');
      }

      // 处理标准视频链接: /watch?v=VIDEO_ID
      if (parsedUrl.pathname === '/watch') {
        return parsedUrl.searchParams.get('v');
      }

      // 处理嵌入链接: /embed/VIDEO_ID
      if (parsedUrl.pathname.startsWith('/embed/')) {
        return parsedUrl.pathname.replace('/embed/', '');
      }
    }

    return null;
  } catch (error) {
    // URL 解析失败，尝试用简单的字符串匹配作为后备方案
    const patterns = [
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/|youtube\.com\/live\/)([a-zA-Z0-9_-]+)/,
      /v=([a-zA-Z0-9_-]+)/,
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) {
        return match[1];
      }
    }

    return null;
  }
}
