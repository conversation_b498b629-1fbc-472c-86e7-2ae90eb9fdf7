import { TextPart } from 'ai';
import { CompletionBlockTypeEnum, ContextProviderType, LLMs, ToolNames } from '@/common/types';
import { GetContextsQuery } from '@/modules/ai/contexts/queries';
import { ToolCompletionBlock } from '@/modules/ai/domain/models/completion-block.entity';
import { Generation } from '@/modules/ai/domain/models/generation.entity';
import { Chat } from '../../domain/chat/models/chat.entity';
import { AssistantMessage, UserMessage } from '../../domain/message/models/message.entity';
import { SendMessageCommand } from '../commands/send-message.command';
import { ChatRunner } from './chat';

// 本地调试用，跳过后面很贵的 tool 调用
const stopAtFactory = false;

// 定义 URL 元数据类型
interface UrlMetadata {
  url?: string;
  title?: string;
  description?: string;
  ogImage?: string;
  ogTitle?: string;
  ogDescription?: string;
  favicon?: string;
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  error?: string;
}

type YouTubeBoardState =
  | 'prefetch-urls'
  | 'create-board'
  | 'create-snip'
  | 'youtube-factory'
  | 'generate-diagram'
  | 'generate-thought'
  | 'stop';

/**
 * YouTube Board Runner - 专门处理 YouTube 链接的简化版 board 创建流程
 * 1. 预抓取 URL 元信息（标题、描述、OG图片等）
 * 2. 创建 board（使用预抓取的信息生成合适的标题和图标）
 * 3. 将 YouTube 链接创建为 snip（同步等待抓取完成）
 * 4. 进入 youtube-factory 生成花里胡哨的东西
 * 5. 进入 generate-diagram 阶段，强制生成图表
 * 6. 进入 generate-thought 阶段，强制生成文章（包含图表）
 * 7. 结束流程
 */
export class YouTubeBoardRunner extends ChatRunner<SendMessageCommand> {
  protected readonly traceName = 'youtube-board-workflow';
  protected readonly prompt = 'youtube-board-prompt';
  protected currentState: YouTubeBoardState = 'prefetch-urls';
  protected userMessage: UserMessage;
  protected assistantMessage: AssistantMessage;
  protected youtubeUrl: string;

  constructor(chat: Chat, userId: string, youtubeUrl: string) {
    super(chat, userId);
    this.youtubeUrl = youtubeUrl;
  }

  protected getToolBlocks(): ToolCompletionBlock[] {
    return this.generations
      .flatMap((g) => g.blocks)
      .filter(
        (b) => b.type === CompletionBlockTypeEnum.TOOL && b.isFinal(),
      ) as ToolCompletionBlock[];
  }

  protected getLastExecutedToolName(): string {
    const toolBlocks = this.getToolBlocks();
    return toolBlocks.slice(-1)[0]?.toolName ?? '';
  }

  protected getCurrentBoardId(): string | null {
    return this.chat.getBoardId();
  }

  protected PrefetchUrlsTools = [ToolNames.PREFETCH_URLS];
  protected async setupPrefetchUrlsGeneration(_command: SendMessageCommand) {
    const { prompt, promptMessages } = await this.promptService.getPromptAndMessages(
      'prefetch-a-url-prompt',
      { url: this.youtubeUrl },
    );

    return Generation.createFromPrompt({
      tools: this.PrefetchUrlsTools,
      toolChoice: 'required', // 强制调用工具
      model: this.assistantMessage.model as LLMs,
      prompt,
      promptMessages,
    });
  }

  protected CreateBoardTools = [ToolNames.CREATE_BOARD];
  protected async setupCreateBoardGeneration(_command: SendMessageCommand) {
    // 获取预抓取的元信息
    const prefetchResults = this.getToolBlocks()
      .filter((b) => b.toolName === ToolNames.PREFETCH_URLS)
      .flatMap((b) => b.toolResult?.metadataList || []);

    const metadata = (prefetchResults[0] as UrlMetadata) || {};

    const content = `title: ${metadata.title || metadata.ogTitle || ''} \n description: ${metadata.description || metadata.ogDescription || ''} \n author: ${metadata.author || ''}`;

    const { prompt, promptMessages } = await this.promptService.getPromptAndMessages(
      'create-board-prompt',
      { user_query: content },
    );

    return Generation.createFromPrompt({
      tools: this.CreateBoardTools,
      toolChoice: 'required', // 强制调用工具
      model: this.assistantMessage.model as LLMs,
      prompt,
      promptMessages,
    });
  }

  protected CreateSnipTools = [ToolNames.CREATE_SNIP_BY_URL];
  protected async setupCreateSnipGeneration(_command: SendMessageCommand) {
    // 保持与 currentGeneration 的配置一致性，但需要设置 toolChoice
    const generation = new Generation({
      model: this.assistantMessage.model as LLMs,
      tools: this.CreateSnipTools,
      toolChoice: 'required', // 强制调用工具
      bizArgs: this.currentGeneration?.bizArgs,
      modelOptions: this.currentGeneration?.modelOptions,
      traceMetadata: this.currentGeneration?.traceMetadata,
    });

    // 使用 YouTube 专用的创建 snip 提示词
    const { prompt, promptMessages } = await this.promptService.getPromptAndMessages(
      'youtube-board-snip-prompt',
      {
        youtube_url: this.youtubeUrl,
        board_id: this.getCurrentBoardId(),
      },
    );

    generation.setPrompt(prompt);
    generation.setPromptMessages([
      ...promptMessages,
      // 包含之前的工具调用结果
      ...this.currentGeneration.generatedMessages,
    ]);

    return generation;
  }

  protected YouTubeFactoryTools = [ToolNames.YOUTUBE_FACTORY];
  protected async setupYouTubeFactoryGeneration(_command: SendMessageCommand) {
    // 保持与 currentGeneration 的配置一致性，但需要设置 toolChoice
    const generation = new Generation({
      model: this.assistantMessage.model as LLMs,
      tools: this.YouTubeFactoryTools,
      toolChoice: 'required', // 强制调用工具
      bizArgs: this.currentGeneration?.bizArgs,
      modelOptions: this.currentGeneration?.modelOptions,
      traceMetadata: this.currentGeneration?.traceMetadata,
    });

    // 使用 YouTube 专用的 factory 提示词
    const { prompt, promptMessages } =
      await this.promptService.getPromptAndMessages('youtube-factory-prompt');
    generation.setPrompt(prompt);
    generation.setPromptMessages([
      ...promptMessages,
      // 包含之前的工具调用结果
      ...this.currentGeneration.generatedMessages,
    ]);
    generation.setTools([ToolNames.YOUTUBE_FACTORY]);

    return generation;
  }

  protected DiagramGenerateTools = [ToolNames.DIAGRAM_GENERATE];
  protected ThoughtGenerateTools = [ToolNames.EDIT_THOUGHT];

  protected toc: string = '';
  protected async getToc() {
    if (!this.toc) {
      const { contextString: toc } = await this.queryBus.execute(
        new GetContextsQuery({
          providerType: ContextProviderType.BOARD_STRUCTURE,
          contextId: this.getCurrentBoardId(),
          options: {
            userId: this.userId,
            spaceId: this.youapiClsService.getSpaceId(),
          },
        }),
      );
      this.toc = toc;
    }
    return this.toc;
  }

  protected objective: string = '';

  protected async getYouTubeFactoryBlock() {
    return this.getToolBlocks().find((b) => b.toolName === ToolNames.YOUTUBE_FACTORY);
  }

  protected async getOverviewGroupId() {
    const youtubeFactoryBlock = await this.getYouTubeFactoryBlock();
    const createdGroups = (youtubeFactoryBlock?.toolResult?.board_groups as any)?.groups;
    return createdGroups?.find((g) => g.name === 'Overview')?.id;
  }

  protected async setupGenerateDiagramGeneration(_command: SendMessageCommand) {
    const generation = new Generation({
      model: this.assistantMessage.model as LLMs,
      tools: this.DiagramGenerateTools,
      toolChoice: 'required', // 强制调用 DIAGRAM_GENERATE
      bizArgs: this.currentGeneration?.bizArgs,
      modelOptions: this.currentGeneration?.modelOptions,
      traceMetadata: this.currentGeneration?.traceMetadata,
    });

    const toc = await this.getToc();

    // 获取 YouTube Factory 的结果作为 reference
    const youtubeFactoryBlock = await this.getYouTubeFactoryBlock();
    const transcriptText = youtubeFactoryBlock?.toolResult?.transcript_text;

    const overviewGroupId = await this.getOverviewGroupId();
    const userQuery = `Please pass the parent_board_group_id: ${overviewGroupId} to diagram_generate tool.`;

    const { prompt, promptMessages } = await this.promptService.getPromptAndMessages(
      'generate-board-draft-prompt',
      {
        toc,
        objective: this.objective,
        user_query: userQuery,
        reference: transcriptText,
      },
    );
    generation.setPrompt(prompt);
    generation.setPromptMessages(promptMessages);

    return generation;
  }

  protected async setupGenerateThoughtGeneration(_command: SendMessageCommand) {
    const generation = new Generation({
      model: this.assistantMessage.model as LLMs,
      tools: this.ThoughtGenerateTools,
      toolChoice: 'required', // 强制调用 EDIT_THOUGHT
      bizArgs: this.currentGeneration?.bizArgs,
      modelOptions: this.currentGeneration?.modelOptions,
      traceMetadata: this.currentGeneration?.traceMetadata,
    });

    const toc = await this.getToc();

    // 获取 YouTube Factory 的结果作为 reference
    const youtubeFactoryBlock = await this.getYouTubeFactoryBlock();
    const transcriptText = youtubeFactoryBlock?.toolResult?.transcript_text;

    const overviewGroupId = await this.getOverviewGroupId();
    const userQuery = `Please pass the parent_board_group_id: ${overviewGroupId} to edit_thought tool.`;

    const { prompt, promptMessages } = await this.promptService.getPromptAndMessages(
      'generate-board-draft-prompt',
      {
        toc,
        objective: this.objective,
        user_query: userQuery,
        reference: transcriptText,
      },
    );

    // 将之前生成的消息（包含图表）包含进来
    generation.setPrompt(prompt);
    generation.setPromptMessages([
      ...promptMessages,
      // 包含之前的工具调用结果，这样模型可以看到已生成的图表
      ...this.currentGeneration.generatedMessages,
    ]);

    return generation;
  }

  protected updateCurrentState() {
    const prevState = this.currentState;

    // 防止已完成的工作流重新开始
    if (this.currentState === 'stop') {
      return;
    }

    // prefetch-urls -> create-board
    const finishedPrefetchUrls =
      this.getLastExecutedToolName() === ToolNames.PREFETCH_URLS &&
      this.currentState === 'prefetch-urls';
    if (finishedPrefetchUrls) {
      this.currentState = 'create-board';
      this.logger.log(
        `State transition: ${prevState} -> ${this.currentState} (finished prefetch urls)`,
      );
      return;
    }

    // create-board -> create-snip
    const finishedCreateBoard =
      this.getLastExecutedToolName() === ToolNames.CREATE_BOARD &&
      this.currentState === 'create-board';
    if (finishedCreateBoard) {
      this.currentState = 'create-snip';
      this.logger.log(
        `State transition: ${prevState} -> ${this.currentState} (finished create board)`,
      );
      return;
    }

    // create-snip -> youtube-factory
    const finishedCreateSnip =
      this.getLastExecutedToolName() === ToolNames.CREATE_SNIP_BY_URL &&
      this.currentState === 'create-snip';
    if (finishedCreateSnip) {
      this.currentState = 'youtube-factory';
      this.logger.log(
        `State transition: ${prevState} -> ${this.currentState} (finished create snip)`,
      );
      return;
    }

    // youtube-factory -> generate-diagram (或调试模式下停止)
    const youtubeFactoryBlockCount = this.getToolBlocks().filter(
      (b) => b.toolName === ToolNames.YOUTUBE_FACTORY,
    ).length;
    const finishedYouTubeFactory =
      this.getLastExecutedToolName() === ToolNames.YOUTUBE_FACTORY &&
      this.currentState === 'youtube-factory' &&
      youtubeFactoryBlockCount >= 1;
    if (finishedYouTubeFactory) {
      // 设置 objective - 从 youtube factory 的结果中提取
      this.objective = this.currentGeneration.generatedMessages
        .flatMap((m) => m.content as TextPart[])
        .filter((c) => c.type === 'text')
        .map((c) => c.text)
        .join('\n\n');

      if (stopAtFactory) {
        this.currentState = 'stop';
        this.logger.log(
          `State transition: ${prevState} -> ${this.currentState} (debug mode: stopped after factory)`,
        );
      } else {
        this.currentState = 'generate-diagram';
        this.logger.log(
          `State transition: ${prevState} -> ${this.currentState} (finished youtube factory)`,
        );
      }
      return;
    }

    // generate-diagram -> generate-thought
    const diagramBlockCount = this.getToolBlocks().filter(
      (b) => b.toolName === ToolNames.DIAGRAM_GENERATE,
    ).length;
    const finishedGenerateDiagram =
      this.getLastExecutedToolName() === ToolNames.DIAGRAM_GENERATE &&
      this.currentState === 'generate-diagram' &&
      diagramBlockCount >= 1;
    if (finishedGenerateDiagram) {
      this.currentState = 'generate-thought';
      this.logger.log(
        `State transition: ${prevState} -> ${this.currentState} (finished generate diagram)`,
      );
      return;
    }

    // generate-thought -> stop
    const thoughtBlockCount = this.getToolBlocks().filter(
      (b) => b.toolName === ToolNames.EDIT_THOUGHT,
    ).length;
    const finishedGenerateThought =
      this.getLastExecutedToolName() === ToolNames.EDIT_THOUGHT &&
      this.currentState === 'generate-thought' &&
      thoughtBlockCount >= 1;
    if (finishedGenerateThought) {
      this.currentState = 'stop';
      this.logger.log(
        `State transition: ${prevState} -> ${this.currentState} (finished generate thought)`,
      );
      return;
    }

    // 没有状态变化时的日志
    if (prevState === this.currentState) {
      this.logger.debug(
        `State remains: ${this.currentState} (lastTool: ${this.getLastExecutedToolName()})`,
      );
    }
  }

  protected shouldStop() {
    return this.currentState === 'stop';
  }

  protected async setupGeneration(command: SendMessageCommand) {
    // 通过 create_board 新建 board 后需更新当前 Chat 实例
    if (!this.getCurrentBoardId()) {
      this.logger.warn('No board id found, fetching new chat detail from db');
      const chat = await this.chatRepository.getById(this.chat.id);
      chat.messages = this.chat.messages;
      this.chat = chat;
    }
    // 初始化 userMessage 和 assistantMessage
    if (!this.userMessage) {
      this.userMessage = this.chat.getLastUserMessage();
      this.assistantMessage = this.chat.getLastAssistantMessage();
    }
    this.assistantMessage.blocks = this.generations.flatMap((g) => g.blocks);

    // 修改当前 state
    this.updateCurrentState();

    // 在 stop 状态时提前返回，避免创建新的 generation
    if (this.currentState === 'stop') {
      this.logger.log('Workflow completed, skipping generation setup');
      return;
    }

    // 确保在 create-snip 状态时有 board_id
    if (this.currentState === 'create-snip' && !this.getCurrentBoardId()) {
      throw new Error('Board ID is required for create-snip state but not found');
    }

    let generation: Generation;

    switch (this.currentState) {
      case 'prefetch-urls':
        generation = await this.setupPrefetchUrlsGeneration(command);
        break;
      case 'create-board':
        generation = await this.setupCreateBoardGeneration(command);
        break;
      case 'create-snip':
        generation = await this.setupCreateSnipGeneration(command);
        break;
      case 'youtube-factory':
        generation = await this.setupYouTubeFactoryGeneration(command);
        break;
      case 'generate-diagram':
        generation = await this.setupGenerateDiagramGeneration(command);
        break;
      case 'generate-thought':
        generation = await this.setupGenerateThoughtGeneration(command);
        break;
      default:
        // 不应该到达这里
        this.logger.error(`Unexpected state: ${this.currentState}`);
        return;
    }

    generation
      .setTraceMetadata(this.chat.getTraceMetadata())
      .setChatMessageId(this.chat.getLastAssistantMessage().id)
      .setBizArgs({
        chatId: this.chat.id,
        userId: this.userId,
        messageId: this.chat.getLastAssistantMessage().id,
      });
    this.addGeneration(generation);
  }
}
