import { MessageModeEnum } from '@/common/types';
import { Chat } from '../../domain/chat/models/chat.entity';
import { AgentRunner } from './agent';
import { AskRunner } from './ask';
import { NewBoardRunner } from './new-board';
import { YouTubeBoardRunner } from './youtube-board';

export { AgentRunner };
export { AskRunner };
export { NewBoardRunner };
export { YouTubeBoardRunner };

export function getChatRunner(chat: Chat, userId: string) {
  const lastAssistantMessage = chat.getLastAssistantMessage();
  if (!lastAssistantMessage) {
    throw new Error('Chat has no assistant message');
  }
  if (lastAssistantMessage.mode === MessageModeEnum.ASK) {
    return new AskRunner(chat, userId);
  }
  return new AgentRunner(chat, userId);
}
