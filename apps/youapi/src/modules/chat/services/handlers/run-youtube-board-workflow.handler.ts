import { Injectable, Logger } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>ler, ICommandHandler } from '@nestjs/cqrs';
import type { Observable } from 'rxjs';
import { AppConfigService } from '@/common/services/app-config.service';
import { YouapiClsService } from '@/common/services/cls.service';
import {
  ChatModeEnum,
  ChatOriginTypeEnum,
  CompletionStreamChunk,
  MessageModeEnum,
  StreamChunkUnion,
} from '@/common/types/chat.types';
import { Chat } from '../../domain/chat/models/chat.entity';
import { AssistantMessage, UserMessage } from '../../domain/message/models/message.entity';
import { ChatRepository } from '../../repositories/chat.repository';
import { RunYouTubeBoardWorkflowCommand } from '../commands/run-youtube-board-workflow.command';
import { SendMessageCommand } from '../commands/send-message.command';
import { YouTubeBoardRunner } from '../runners/youtube-board';

@CommandHandler(RunYouTubeBoardWorkflowCommand)
@Injectable()
export class RunYouTubeBoardWorkflowHandler
  implements ICommandHandler<RunYouTubeBoardWorkflowCommand>
{
  private readonly logger = new Logger(RunYouTubeBoardWorkflowHandler.name);

  constructor(
    private readonly chatRepository: ChatRepository,
    private readonly youapiClsService: YouapiClsService,
    private readonly appConfigService: AppConfigService,
  ) {}

  async execute(
    command: RunYouTubeBoardWorkflowCommand,
  ): Promise<Observable<CompletionStreamChunk<StreamChunkUnion>>> {
    const { userId, message, youtubeUrl } = command.param;

    // 创建新的 chat
    const chat = Chat.createNew({
      mode: ChatModeEnum.NEW_BOARD,
      creatorId: userId,
      origin: {
        type: ChatOriginTypeEnum.UNKNOWN,
      },
    });
    await this.chatRepository.save(chat);

    // 创建用户消息
    const userMessage = UserMessage.createNew({
      chatId: chat.id,
      message: message,
      origin: {
        type: ChatOriginTypeEnum.UNKNOWN,
      },
      mode: MessageModeEnum.ASK,
      model: this.appConfigService.getDefaultAgentModel(),
    });
    const assistantMessage = AssistantMessage.createNewFromUserMessage(userMessage);
    assistantMessage.setTraceId(this.youapiClsService.getTraceId());

    chat.addMessage(userMessage);
    chat.addMessage(assistantMessage);

    // 使用 YouTube 专用的 runner
    const runner = new YouTubeBoardRunner(chat, userId, youtubeUrl);
    const observable = await runner.generate(
      new SendMessageCommand({
        userId,
        chatId: chat.id,
        message: message,
      }),
    );
    return observable;
  }
}
