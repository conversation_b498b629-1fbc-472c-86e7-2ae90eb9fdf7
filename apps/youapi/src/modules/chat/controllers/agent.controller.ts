/**
 * Chat Assistant V2 Controller - V2 聊天助手控制器
 * 处理 V2 API 聊天助手相关的HTTP请求
 *
 * Migrated from:
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/app/api/v2/chatAssistant/getChatDetail/route.ts
 */

import { Body, Controller, HttpCode, Post } from '@nestjs/common';
import { ApiBody, ApiExtraModels, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { YouapiSse } from '@/common/sse';
import { BaseController } from '@/shared/base.controller';
import { RunNewBoardWorkflowDto } from '../dto/agent.dto';
import {
  CompletionBlockDto,
  CompletionChatDto,
  CompletionChunkResponseSchema,
  CompletionMessageDto,
  CompletionStreamAppendJsonChunkDto,
  CompletionStreamAppendStringChunkDto,
  CompletionStreamErrorChunkDto,
  CompletionStreamInsertChunkDto,
  CompletionStreamReplaceChunkDto,
} from '../dto/completion-stream.dto';
import {
  AssistantMessageV2Dto,
  ContentBlockV2Dto,
  ReasoningBlockV2Dto,
  ToolBlockV2Dto,
  UserMessageV2Dto,
} from '../dto/v2/chat-v2.dto';
import { RunNewBoardWorkflowCommand } from '../services/commands/run-new-board-workflow.command';
import { RunYouTubeBoardWorkflowCommand } from '../services/commands/run-youtube-board-workflow.command';
import { detectYouTubeUrl } from '../utils/youtube-detector';

@ApiTags('Agent')
@ApiExtraModels(
  CompletionStreamErrorChunkDto,
  CompletionStreamInsertChunkDto,
  CompletionStreamReplaceChunkDto,
  CompletionStreamAppendStringChunkDto,
  CompletionStreamAppendJsonChunkDto,
  CompletionChatDto,
  CompletionMessageDto,
  CompletionBlockDto,
  UserMessageV2Dto,
  AssistantMessageV2Dto,
  ToolBlockV2Dto,
  ContentBlockV2Dto,
  ReasoningBlockV2Dto,
)
@Controller('api/v2/agent')
export class AgentController extends BaseController {
  @Post('newBoard/run')
  @HttpCode(200)
  @ApiOperation({
    summary: 'Run new board workflow',
    description:
      'Runs a new board workflow and returns a server-sent event stream with completion chunks',
  })
  @ApiBody({ type: RunNewBoardWorkflowDto })
  @ApiResponse({
    status: 200,
    content: CompletionChunkResponseSchema,
  })
  @YouapiSse()
  async runNewBoardWorkflow(@Body() dto: RunNewBoardWorkflowDto) {
    const userId = this.getUserId();

    // 检测是否包含 YouTube URL
    const youtubeUrl = detectYouTubeUrl(dto.message);

    if (youtubeUrl && process.env.ENABLE_YOUTUBE_NEW_BOARD === '1') {
      // 如果检测到 YouTube URL，走 YouTube Board 流程
      const command = new RunYouTubeBoardWorkflowCommand({
        userId,
        message: dto.message,
        youtubeUrl,
      });
      return await this.commandBus.execute(command);
    } else {
      // 否则走正常的 new board 流程
      const command = new RunNewBoardWorkflowCommand({
        userId,
        message: dto.message,
      });
      return await this.commandBus.execute(command);
    }
  }
}
