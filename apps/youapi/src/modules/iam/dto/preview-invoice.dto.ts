import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import Stripe from 'stripe';

class LineItemDto {
  @ApiProperty({
    description: '账单项标题',
    example: 'Upgrade to max monthly with immediate effect',
  })
  title: string;

  @ApiProperty({
    description: '账单项描述',
    example: 'Upgrade to max monthly with immediate effect',
  })
  description: string;

  @ApiProperty({
    description: '账单项金额',
    example: 100,
  })
  amount: number;
}

class CardDto {
  @ApiProperty({
    description: '卡品牌',
    example: 'visa',
  })
  brand: string;

  @ApiProperty({
    description: '卡号',
    example: '4242',
  })
  last4: string;
}
class PaymentMethodDto {
  @ApiProperty({
    description: '支付方式类型',
    example: 'card',
  })
  type: Stripe.PaymentMethod.Type;

  @ApiProperty({
    description: '卡信息',
    type: CardDto,
  })
  card?: CardDto;
}

export class PreviewInvoiceDto {
  @ApiProperty({
    description: '小计',
    example: 100,
  })
  subtotal: number;

  @ApiPropertyOptional({
    description: '折扣',
    example: 10,
  })
  discount?: number;

  @ApiProperty({
    description: '总计',
    example: 100,
  })
  total: number;

  @ApiProperty({
    description: '预览账单项',
    type: [LineItemDto],
  })
  lines: LineItemDto[];

  @ApiPropertyOptional({
    description: '默认支付方式',
    type: PaymentMethodDto,
  })
  defaultPaymentMethod?: PaymentMethodDto;
}
