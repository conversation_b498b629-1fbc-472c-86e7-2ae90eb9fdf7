import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { Transactional } from '@/common/database/transaction.decorator';
import { SubscriptionProvider } from '@/modules/iam/domain/subscription/models/subscription.types';
import { CreditAccountRepository } from '@/modules/iam/repositories/credit-account.repository';
import { CreditTransactionRepository } from '@/modules/iam/repositories/credit-transaction.repository';
import { SpaceDto } from '../../../dto/space.dto';
import { SpaceRepository } from '../../../repositories/space.repository';
import { SubscriptionRepository } from '../../../repositories/subscription.repository';
import { deleteSubscriptionForTestCommand } from '../../commands/subscription/delete-subscription-for-test.command';
import { StripeService } from '../../stripe.service';
import { UserDtoService } from '../../user-dto.service';

@CommandHandler(deleteSubscriptionForTestCommand)
export class DeleteSubscriptionForTestHandler
  implements ICommandHandler<deleteSubscriptionForTestCommand>
{
  constructor(
    private readonly spaceRepository: SpaceRepository,
    private readonly subscriptionRepository: SubscriptionRepository,
    private readonly userDtoService: UserDtoService,
    private readonly creditAccountRepository: CreditAccountRepository,
    private readonly creditTransactionRepository: CreditTransactionRepository,
    private readonly stripeService: StripeService,
  ) {}

  @Transactional()
  async execute(command: deleteSubscriptionForTestCommand): Promise<SpaceDto> {
    const { spaceId } = command;

    // 获取并清除订阅
    const subscription = await this.subscriptionRepository.findBySpaceId(spaceId);
    if (subscription) {
      if (subscription.provider === SubscriptionProvider.STRIPE) {
        const stripeSubscriptionId = subscription.getStripeSubscriptionId();
        await this.stripeService.cancelSubscriptionNow(stripeSubscriptionId);
      }

      await this.subscriptionRepository.deleteById(subscription.id);
      const creditAccount = await this.creditAccountRepository.getBySpaceIdForUpdate(spaceId);
      const now = new Date();
      const nextMonth = new Date(now);
      nextMonth.setMonth(nextMonth.getMonth() + 1);

      const transactions = creditAccount.resetToFree();
      if (transactions.length > 0) {
        await this.creditTransactionRepository.createMany(transactions);
        await this.creditAccountRepository.save(creditAccount);
      }
    }

    // 返回更新后的 space 信息
    const space = await this.spaceRepository.getById(spaceId);
    return this.userDtoService.toSpaceDto(space);
  }
}
