import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { format } from 'date-fns';
import Stripe from 'stripe';
import {
  BillingInterval,
  ProductTier,
  StripePriceLookupKey,
} from '../domain/subscription/models/subscription.types';
import { StripeHolder } from './stripe.holder';

/**
 * StripeService
 * 基于设计文档的 Stripe API 封装服务
 * 提供订阅管理、客户管理、价格查询等功能
 */
@Injectable()
export class StripeService {
  private readonly logger = new Logger(StripeService.name);

  // Subscription expand 配置常量
  private static readonly SUBSCRIPTION_DETAILED_EXPAND = [
    'items.data.price',
    'latest_invoice',
    'schedule',
    'schedule.phases.items.price',
    'discounts',
    'default_payment_method',
  ];

  constructor(private readonly stripeHolder: StripeHolder) {}

  /**
   * 获取默认的 Stripe 客户端
   */
  private getStripe(): Stripe {
    return this.stripeHolder.hongkong;
  }

  getPriceLookupKey(
    productTier: ProductTier,
    billingInterval: BillingInterval,
    timeZone: string,
  ): StripePriceLookupKey {
    // const isChinaMainland = isChinaMainlandTimeZone(timeZone);
    // 统一走全球定价
    const isChinaMainland = false;

    if (productTier === ProductTier.PRO) {
      if (billingInterval === BillingInterval.MONTHLY) {
        if (isChinaMainland) {
          throw new BadRequestException('Monthly pro is not available in China mainland');
        }
        return StripePriceLookupKey.YOUMIND_PRO_MONTHLY;
      } else {
        return isChinaMainland
          ? StripePriceLookupKey.YOUMIND_PRO_YEARLY_CNY
          : StripePriceLookupKey.YOUMIND_PRO_YEARLY;
      }
    } else if (productTier === ProductTier.MAX) {
      if (billingInterval === BillingInterval.MONTHLY) {
        if (isChinaMainland) {
          throw new BadRequestException('Monthly max is not available in China mainland');
        }
        return StripePriceLookupKey.YOUMIND_MAX_MONTHLY;
      } else {
        return isChinaMainland
          ? StripePriceLookupKey.YOUMIND_MAX_YEARLY_CNY
          : StripePriceLookupKey.YOUMIND_MAX_YEARLY;
      }
    }

    throw new BadRequestException(`Invalid product tier: ${productTier}`);
  }

  /**
   * 根据 lookup key 获取价格信息
   */
  async getPriceByLookupKey(lookupKey: string): Promise<Stripe.Price | null> {
    const stripe = this.getStripe();

    const prices = await stripe.prices.list({
      lookup_keys: [lookupKey],
      active: true,
      limit: 1,
    });

    if (prices.data.length === 0) {
      throw new BadRequestException(`Price not found for ${lookupKey}`);
    }

    return prices.data[0];
  }

  async getPrice(priceId: string): Promise<Stripe.Price | undefined> {
    const stripe = this.getStripe();
    const price = await stripe.prices.retrieve(priceId);
    if (!price) {
      throw new BadRequestException(`Price not found for ${priceId}`);
    }
    return price;
  }

  /**
   * 创建 Stripe 客户
   */
  async createCustomer(
    email: string,
    userId: string,
    testClockId?: string,
  ): Promise<Stripe.Customer> {
    const stripe = this.getStripe();

    return await stripe.customers.create({
      email,
      metadata: {
        user_id: userId,
      },
      test_clock: testClockId,
    });
  }

  /**
   * 创建 Stripe 订阅
   */
  async createSubscription(
    customerId: string,
    priceId: string,
    spaceId: string,
  ): Promise<Stripe.Subscription> {
    const stripe = this.getStripe();

    return await stripe.subscriptions.create({
      customer: customerId,
      items: [{ price: priceId }],
      metadata: {
        space_id: spaceId,
      },
      // 直接付款失败，上层会走 checkout 流程
      payment_behavior: 'error_if_incomplete',
      expand: StripeService.SUBSCRIPTION_DETAILED_EXPAND,
    });
  }

  /**
   * 升级
   */
  async upgrade(subscriptionId: string, priceId: string): Promise<Stripe.Subscription> {
    const stripe = this.getStripe();

    const subscription = await stripe.subscriptions.retrieve(subscriptionId);

    /**
     * TODO payment_behavior 默认是 allow_incomplete
     * 当升级的扣款失败时，订阅会直接进入 past_due 状态，并且有一个 unpaid_invoice
     * 如果重试期内未付款，会自动取消订阅
     *
     * 极端情况下，用户当期产品还没怎么用，就仓促升级的话：
     * - 如果升级成功，当期剩余时长会抵扣到升级的账单时，还算合理
     * - 如果升级最终失败，当期剩余时长并不会退款，用户在 past_due 的宽限期内还可以使用当期的积分，但这个宽限期是两周左右，短于一个月
     */
    return stripe.subscriptions.update(subscriptionId, {
      items: [{ id: subscription.items.data[0].id, price: priceId }],
      // 立即升级，重置账单周期，立即按比例处理退款和账单
      billing_cycle_anchor: 'now',
      cancel_at_period_end: false,
      // 设置 off_session 为 true，否则 alipay 会要求提供 return url
      off_session: true,
      expand: StripeService.SUBSCRIPTION_DETAILED_EXPAND,
    });
  }

  /**
   * 获取 Stripe 订阅详情
   */
  async getSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
    const stripe = this.getStripe();

    return stripe.subscriptions.retrieve(subscriptionId, {
      expand: StripeService.SUBSCRIPTION_DETAILED_EXPAND,
    });
  }

  /**
   * 根据空间 ID 获取 Stripe 订阅
   */
  async findSubscriptionBySpaceId(spaceId: string): Promise<Stripe.Subscription | undefined> {
    const stripe = this.getStripe();

    const subscriptions = await stripe.subscriptions.search({
      query: `metadata["space_id"]:"${spaceId}"`,
      expand: ['data.items.data.price', 'data.schedule', 'data.schedule.phases.items.price'],
    });

    return subscriptions.data[0];
  }

  /**
   * 用 Subscription Schedule 延期变更
   */
  async scheduleUpdateSubscriptionAtPeriodEnd(
    subscriptionId: string,
    priceId: string,
    discounts?: Stripe.Discount[],
  ): Promise<Stripe.SubscriptionSchedule> {
    const stripe = this.getStripe();

    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    const currentItem = subscription.items.data[0];

    let scheduleId = subscription.schedule as string;
    if (!scheduleId) {
      const schedule = await stripe.subscriptionSchedules.create({
        from_subscription: subscriptionId,
      });
      scheduleId = schedule.id;
    }

    // 分成两个阶段，第一阶段保持当前订阅到周期结束，第二阶段使用新价格
    return stripe.subscriptionSchedules.update(scheduleId, {
      phases: [
        {
          items: [{ price: currentItem.price.id }],
          start_date: currentItem.current_period_start,
          end_date: currentItem.current_period_end,
          discounts: discounts?.map((discount) => ({ discount: discount.id })),
        },
        {
          items: [{ price: priceId }],
          start_date: currentItem.current_period_end,
          // 注意这里只保留未过期的折扣，否则会报错
          discounts: discounts
            ?.filter((discount) => discount.end > currentItem.current_period_end)
            .map((discount) => ({ discount: discount.id })),
        },
      ],
      expand: ['phases.items.price'],
    });
  }

  /**
   * 释放 Subscription Schedule，取消延期变更
   */
  async releaseSubscriptionSchedule(scheduleId: string): Promise<Stripe.SubscriptionSchedule> {
    const stripe = this.getStripe();
    return stripe.subscriptionSchedules.release(scheduleId);
  }

  /**
   * 设置订阅在周期结束时取消
   */
  async cancelSubscriptionAtPeriodEnd(subscriptionId: string): Promise<Stripe.Subscription> {
    const stripe = this.getStripe();

    return stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: true,
      expand: StripeService.SUBSCRIPTION_DETAILED_EXPAND,
    });
  }

  /**
   * 设置订阅在周期结束时取消
   */
  async cancelSubscriptionNow(subscriptionId: string): Promise<Stripe.Subscription> {
    const stripe = this.getStripe();

    return stripe.subscriptions.cancel(subscriptionId, {
      expand: StripeService.SUBSCRIPTION_DETAILED_EXPAND,
    });
  }

  /**
   * 取消到期变更
   */
  async doNotCancelAtPeriodEnd(subscriptionId: string): Promise<Stripe.Subscription> {
    const stripe = this.getStripe();

    return stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: false,
      expand: StripeService.SUBSCRIPTION_DETAILED_EXPAND,
    });
  }

  /**
   * 立即取消订阅
   */
  async cancelSubscriptionImmediately(subscriptionId: string): Promise<Stripe.Subscription> {
    const stripe = this.getStripe();

    return await stripe.subscriptions.cancel(subscriptionId, {
      expand: StripeService.SUBSCRIPTION_DETAILED_EXPAND,
    });
  }

  /**
   * 创建 Checkout Session
   */
  async createCheckoutSession(
    customerId: string,
    priceId: string,
    successUrl: string,
    cancelUrl?: string,
    subscriptionMetadata?: Record<string, string>,
  ): Promise<Stripe.Checkout.Session> {
    const stripe = this.getStripe();

    return await stripe.checkout.sessions.create({
      customer: customerId,
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      subscription_data: {
        metadata: subscriptionMetadata,
      },
      allow_promotion_codes: true,
      success_url: successUrl,
      cancel_url: cancelUrl || successUrl,
    });
  }

  /**
   * 获取客户信息
   */
  async getCustomer(customerId: string): Promise<Stripe.Customer> {
    const stripe = this.getStripe();

    return (await stripe.customers.retrieve(customerId)) as Stripe.Customer;
  }

  /**
   * 更新客户信息
   */
  async updateCustomer(
    customerId: string,
    updateParams: {
      email?: string;
      metadata?: Record<string, string>;
    },
  ): Promise<Stripe.Customer> {
    const stripe = this.getStripe();

    return await stripe.customers.update(customerId, updateParams);
  }

  /**
   * 获取订阅的 Subscription Schedule
   */
  async getSubscriptionSchedule(scheduleId: string): Promise<Stripe.SubscriptionSchedule> {
    const stripe = this.getStripe();

    return await stripe.subscriptionSchedules.retrieve(scheduleId);
  }

  /**
   * 取消 Subscription Schedule
   */
  async cancelSubscriptionSchedule(scheduleId: string): Promise<Stripe.SubscriptionSchedule> {
    const stripe = this.getStripe();

    return await stripe.subscriptionSchedules.cancel(scheduleId);
  }

  /**
   * 创建升级预览账单
   */
  async createUpgradePreviewInvoice(
    customerId: string,
    subscriptionId: string,
    itemId: string,
    priceId: string,
  ): Promise<Stripe.Invoice> {
    const stripe = this.getStripe();

    return stripe.invoices.createPreview({
      customer: customerId,
      subscription: subscriptionId,
      subscription_details: {
        items: [{ id: itemId, price: priceId }],
        // 立即升级，重置账单周期，立即按比例处理退款和账单
        billing_cycle_anchor: 'now',
      },
      expand: ['total_discount_amounts.discount'],
    });
  }

  /**
   * 创建测试时钟
   */
  async createTestClock(name?: string, date?: Date): Promise<Stripe.TestHelpers.TestClock> {
    const stripe = this.getStripe();
    const frozenDate = date ?? new Date();
    return await stripe.testHelpers.testClocks.create({
      name,
      frozen_time: Math.floor(frozenDate.getTime() / 1000),
    });
  }

  /**
   * 获取 Test Clock
   */
  async findTestClock(testClockId: string): Promise<Stripe.TestHelpers.TestClock | undefined> {
    const stripe = this.getStripe();
    return stripe.testHelpers.testClocks.retrieve(testClockId);
  }

  async getTestClock(testClockId: string): Promise<Stripe.TestHelpers.TestClock> {
    const stripe = this.getStripe();
    const testClock = await stripe.testHelpers.testClocks.retrieve(testClockId);
    if (!testClock) {
      throw new NotFoundException(`Test clock ${testClockId} not found`);
    }
    return testClock;
  }

  async advanceTestClock(testClockId: string, fozenTime: Date) {
    const testClock = await this.findTestClock(testClockId);
    if (!testClock) {
      throw new BadRequestException(`Test clock ${testClockId} not found`);
    }

    const stripe = this.getStripe();
    this.logger.log(`Advancing test clock ${testClock.id} to ${fozenTime}`);
    const beforeTime = this.formatFrozenTime(testClock.frozen_time);
    this.logger.log(`Test clock frozen time before advance: ${beforeTime}`);

    const result = await stripe.testHelpers.testClocks.advance(testClock.id, {
      frozen_time: Math.floor(fozenTime.getTime() / 1000),
    });

    const afterTime = this.formatFrozenTime(result.frozen_time);
    this.logger.log(`Test clock frozen time after advance: ${afterTime}`);
    return result;
  }

  async advanceTestClockByMinutes(testClockId: string, minutes: number) {
    const testClock = await this.findTestClock(testClockId);
    if (!testClock) {
      throw new BadRequestException(`Test clock ${testClockId} not found`);
    }

    const stripe = this.getStripe();
    const beforeTime = this.formatFrozenTime(testClock.frozen_time);
    this.logger.log(`Advancing test clock ${testClock.id} by ${minutes} minutes`);
    this.logger.log(`Test clock frozen time before advance: ${beforeTime}`);

    const result = await stripe.testHelpers.testClocks.advance(testClock.id, {
      frozen_time: testClock.frozen_time + minutes * 60,
    });

    const afterTime = this.formatFrozenTime(result.frozen_time);
    this.logger.log(`Test clock frozen time after advance: ${afterTime}`);
    return result;
  }

  /**
   * 格式化 frozen_time 为人类易读的格式
   * @param frozenTime Unix 时间戳（秒）
   * @returns 格式化的时间字符串
   */
  private formatFrozenTime(frozenTime: number): string {
    const date = new Date(frozenTime * 1000); // 转换为毫秒
    const formatted = format(date, 'yyyy-MM-dd HH:mm:ss');
    return `${formatted} (${frozenTime})`;
  }

  /**
   * 构造 webhook 事件
   */
  constructWebhookEvent(
    payload: string | Buffer,
    signature: string,
    endpointSecret: string,
  ): Stripe.Event {
    const stripe = this.getStripe();

    return stripe.webhooks.constructEvent(payload, signature, endpointSecret);
  }
}
