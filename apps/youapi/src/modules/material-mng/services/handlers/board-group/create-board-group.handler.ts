// TODO: 权限验证逻辑已暂时移除，等 user 和 space 模块迁移完成后重新添加

import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { uuidv7 } from 'uuidv7';
import { AuthorizationService } from '@/modules/iam/services/authorization.service';
import { BoardGroup } from '../../../domain/board-group/models/board-group.entity';
import { BoardPositionDomainService } from '../../../domain/shared/board-position.service';
import { BoardGroupDto } from '../../../dto/board-group.dto';
import { BoardRepository } from '../../../repositories/board.repository';
import { BoardGroupRepository } from '../../../repositories/board-group.repository';
import { CreateBoardGroupCommand } from '../../commands/board-group/create-board-group.command';
import { BoardGroupDtoService } from '../../dto-services/board-group-dto.service';

@CommandHandler(CreateBoardGroupCommand)
export class CreateBoardGroupHandler implements ICommandHandler<CreateBoardGroupCommand> {
  constructor(
    private readonly boardGroupRepository: BoardGroupRepository,
    private readonly boardPositionService: BoardPositionDomainService,
    private readonly boardGroupDtoService: BoardGroupDtoService,
    private readonly boardRepository: BoardRepository,
    private readonly authorizationService: AuthorizationService,
  ) {}

  async execute(command: CreateBoardGroupCommand): Promise<BoardGroupDto> {
    const { userId, boardId, name, icon, type } = command;

    // 验证用户对 board 的访问权限
    const board = await this.boardRepository.getById(boardId);
    this.authorizationService.validateSpacePermission(board.spaceId);

    // 创建 board group
    const boardGroupId = uuidv7();
    const boardGroup = await BoardGroup.create(
      boardGroupId,
      userId,
      boardId,
      name,
      icon,
      type,
      this.boardPositionService, // 传递位置服务
    );

    // 保存 board group
    await this.boardGroupRepository.save(boardGroup);

    // 重新获取保存后的 board group 以包含 board_item 信息
    const savedBoardGroup = await this.boardGroupRepository.getById(boardGroupId);

    return this.boardGroupDtoService.toDto(savedBoardGroup);
  }
}
