/**
 * Add Punctuation Handler - 添加标点符号处理器
 * 从 youapp 迁移的添加标点符号处理器
 *
 * Migrated from:
 * - /youapp/src/lib/app/snip/index.ts (addPunctuation)
 */

import { Injectable, Logger } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { SubtitleHandler } from '@repo/common/content/subtitle';
import { runInBackground } from '@/common/errors/error-handler';
import { ContentFormatEnum, ProcessStatusEnum } from '@/common/types';
import { estimateTokens, SafeParse, safeDecodeURIComponent } from '@/common/utils';
import { InsertBlockContentParam } from '@/dao/content/types';
import { BlockDomainService } from '@/domain/block';
import { ContentDomainService } from '@/domain/content';
import { Content } from '@/domain/content/types';
import { SnipDomainService } from '@/domain/snip';
import { TextRunnerService } from '@/modules/ai/runners/service/text-runner.service';
import { AuthorizationService } from '@/modules/iam/services/authorization.service';
import { AddPunctuationCommand } from '../../commands/snip/add-punctuation.command';

@CommandHandler(AddPunctuationCommand)
@Injectable()
export class AddPunctuationHandler implements ICommandHandler<AddPunctuationCommand> {
  private readonly logger = new Logger(AddPunctuationHandler.name);

  constructor(
    private readonly snipDomainService: SnipDomainService,
    private readonly blockDomainService: BlockDomainService,
    private readonly contentDomainService: ContentDomainService,
    private readonly textRunnerService: TextRunnerService,
    private readonly authorizationService: AuthorizationService,
  ) {}

  /*
   * TODO 平迁代码，需要优化
   */
  async execute(command: AddPunctuationCommand): Promise<void> {
    const { snipId, blockId, language, regenerate, userId, spaceId } = command;

    this.logger.debug(
      `Adding punctuation for snip: ${snipId}, block: ${blockId}, language: ${language}`,
    );

    // 1. 获取 snip 并验证权限
    const snip = await this.snipDomainService.getById(snipId);
    if (!snip) {
      throw new Error(`Snip not found: ${snipId}`);
    }
    this.authorizationService.validateSpacePermission(snip.space_id);

    // 2. 查询转录内容
    const transcripts = await this.blockDomainService.queryTranscriptContentBySnip({
      snip_id: snipId,
      status: [ProcessStatusEnum.DONE, ProcessStatusEnum.ING],
    });

    // 3. 检查是否已存在格式化内容
    const formattedTranscripts = transcripts.filter(
      (t) =>
        t.language === language &&
        t.format === ContentFormatEnum.SUBTITLE_FORMATTED &&
        t.status === ProcessStatusEnum.DONE,
    );

    if (formattedTranscripts.length > 0 && !regenerate) {
      this.logger.debug(
        `Formatted transcript already exists for snip: ${snipId}, language: ${language}`,
      );
      return;
    }

    // 4. 查找原始字幕内容
    const transcript = transcripts.find(
      (t) =>
        t.language === language &&
        t.format === ContentFormatEnum.SUBTITLE &&
        t.status === ProcessStatusEnum.DONE,
    );

    if (!transcript) {
      this.logger.warn(`No transcript found for snip: ${snipId}, language: ${language}`);
      return;
    }

    // 5. 使用 SubtitleHandler 解析字幕
    const handler = SubtitleHandler.fromRaw(transcript.raw || '');
    const cues = handler.getCues();

    // 6. 转换为格式化的 cues（包含时间戳和说话人信息）
    const formattedCues: { content: string; speaker: string }[] = [];
    cues.forEach((cue) => {
      const { start, speaker } = cue;
      const content = cue.content.join('\n');
      formattedCues.push({
        content: `[${content}](#${handler.toLlmTimestamp(start)}${
          speaker ? `-${safeDecodeURIComponent(speaker)}` : ''
        })`,
        speaker: speaker ?? '',
      });
    });

    // 7. 分块处理
    const tokensPerChunk = 3000;
    const firstPreviewChunkToken = 1000;
    const chunks: string[] = [];
    let text = '';

    formattedCues.forEach((cue) => {
      const { content } = cue;
      const textToken = estimateTokens(text);
      const contentToken = estimateTokens(content);
      const tokenCount = chunks.length === 0 ? firstPreviewChunkToken : tokensPerChunk;

      if (textToken + contentToken > tokenCount) {
        chunks.push(text);
        text = '';
      }
      text += content;
      text += '\n';
    });

    if (text) {
      chunks.push(text);
    }

    if (!chunks.length) {
      this.logger.warn(`No chunks to process for snip: ${snipId}`);
      return;
    }

    // 8. 异步处理任务
    const asyncTask = async () => {
      let insertedContent: Content | undefined;

      try {
        // 处理第一块以提供快速预览
        const firstChunk = chunks[0];
        this.logger.debug(
          `[addPunctuation] Processing first chunk for snip_id: ${snipId}. Chunk size: ${firstChunk?.length}`,
        );

        // 从 snip.extra 中提取实体名称
        const extra = SafeParse(snip.extra ?? '{}') as Record<string, unknown>;
        const name_candidates =
          'entity_names' in extra && Array.isArray(extra.entity_names)
            ? extra.entity_names.map((n: any) => n.trim()).join('\n')
            : '';

        // 调用 LLM 处理第一块
        const runner = this.textRunnerService.getGeneratePunctuationRunner({
          text: firstChunk,
          name_candidates,
        });
        runner.setRegenerate(!!regenerate).updateMetadata({
          snipId,
        });
        const formattedFirstChunk = await runner.generateOnce();

        const firstChunkFormattedContent = formattedFirstChunk.text;
        if (!firstChunkFormattedContent) {
          this.logger.warn(
            `[addPunctuation] No content found in first chunk result for snip_id: ${snipId}`,
          );
          return;
        }

        // 插入初始内容
        this.logger.debug(
          `[addPunctuation] Inserting initial block content for snip_id: ${snipId}, block_id: ${blockId}`,
        );
        insertedContent = await this.blockDomainService.insertBlockContent(<
          InsertBlockContentParam
        >{
          block_id: blockId,
          snip_id: snipId,
          format: ContentFormatEnum.SUBTITLE_FORMATTED,
          raw: firstChunkFormattedContent,
          plain: firstChunkFormattedContent,
          language,
          status: ProcessStatusEnum.ING,
        });

        this.logger.debug(
          `[addPunctuation] Inserted initial content with ID: ${insertedContent.id} for snip_id: ${snipId}`,
        );

        // 处理剩余块
        if (chunks.length > 1) {
          const now = Date.now();
          this.logger.debug(
            `[addPunctuation] Generating formatted transcript for remaining ${chunks.length - 1} chunks for snip_id: ${snipId}`,
          );

          const remainingChunks = chunks.slice(1);
          const formattedContents = await Promise.all(
            remainingChunks.map((chunk, index) => {
              this.logger.debug(
                `[addPunctuation] Processing chunk ${index + 1}/${remainingChunks.length} for snip_id: ${snipId}. Chunk size: ${chunk?.length}`,
              );
              const runner = this.textRunnerService.getGeneratePunctuationRunner({
                text: chunk,
                name_candidates,
              });
              runner.setRegenerate(!!regenerate).updateMetadata({
                snipId,
              });
              return runner.generateOnce();
            }),
          );

          this.logger.debug(
            `[addPunctuation] Generation for remaining chunks for snip_id: ${snipId} completed in ${
              Date.now() - now
            }ms. Received ${formattedContents.length} results.`,
          );

          // 合并所有格式化内容
          const finalFormattedContent = formattedContents
            .map((l, index) => {
              const content = l.text;
              this.logger.debug(
                `[addPunctuation] Result from chunk ${index + 1}/${remainingChunks.length} for snip_id: ${snipId}:`,
                content?.substring(0, 100) + '...',
              );
              return content;
            })
            .filter((l): l is string => typeof l === 'string' && l.trim().length > 0)
            .join('\n');

          // 更新内容为完整的格式化内容
          this.logger.debug(
            `[addPunctuation] Updating content ID: ${insertedContent.id} with final formatted content (length: ${finalFormattedContent.length}) for snip_id: ${snipId}`,
          );
          await this.contentDomainService.updateContent(insertedContent.id, {
            raw: `${firstChunkFormattedContent}\n${finalFormattedContent}`,
            plain: `${firstChunkFormattedContent}\n${finalFormattedContent}`,
            format: ContentFormatEnum.SUBTITLE_FORMATTED,
            status: ProcessStatusEnum.DONE,
          } as any);
        } else {
          // 只有一块，直接标记为完成
          await this.contentDomainService.updateContent(insertedContent.id, {
            status: ProcessStatusEnum.DONE,
          } as any);
        }

        this.logger.debug(
          `[addPunctuation] Successfully updated content ID: ${insertedContent.id} for snip_id: ${snipId}`,
        );
      } catch (error) {
        this.logger.error(
          `[addPunctuation] Error processing snip_id: ${snipId}, block_id: ${blockId}, language: ${language}`,
          error,
        );

        // 出错时删除已插入的内容
        if (insertedContent) {
          await this.contentDomainService.deleteManyContents([insertedContent.id]);
        }

        throw error;
      }
    };

    // 在后台执行异步任务
    runInBackground(asyncTask());
  }
}
