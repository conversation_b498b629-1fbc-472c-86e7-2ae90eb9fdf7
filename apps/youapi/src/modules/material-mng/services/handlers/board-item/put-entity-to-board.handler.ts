/**
 * Put Entity To Board Handler - 将实体声明到看板处理器
 *
 * Migrated from:
 * - /youapp/src/lib/domain/board-item/index.ts (putEntityToBoard method)
 */

import { Logger } from '@nestjs/common';
import { CommandBus, CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { uuidv7 } from 'uuidv7';
import { runInBackground } from '@/common/errors/error-handler';
import { rankBetween } from '@/common/utils/ranking';
import { AuthorizationService } from '@/modules/iam/services/authorization.service';
import { BoardItem } from '../../../domain/board-item/models/board-item.entity';
import { BoardRepository } from '../../../repositories/board.repository';
import { BoardItemRepository } from '../../../repositories/board-item.repository';
import { UpdateBoardUpdatedAtCommand } from '../../commands/board/update-board-updated-at.command';
import { PutEntityToBoardCommand } from '../../commands/board-item/put-entity-to-board.command';

@CommandHandler(PutEntityToBoardCommand)
export class PutEntityToBoardHandler
  implements ICommandHandler<PutEntityToBoardCommand, BoardItem>
{
  private static readonly logger = new Logger(PutEntityToBoardHandler.name);

  constructor(
    private readonly boardItemRepository: BoardItemRepository,
    private readonly boardRepository: BoardRepository,
    private readonly commandBus: CommandBus,
    private readonly authorizationService: AuthorizationService,
  ) {}

  async execute(command: PutEntityToBoardCommand): Promise<BoardItem> {
    const { boardId, entityType, entityId, parentBoardGroupId, rankAfter } = command;

    // 1. 权限验证 - 验证用户是否有权限访问目标 Board
    const board = await this.boardRepository.getById(boardId);
    this.authorizationService.validateSpacePermission(board.spaceId);

    // 2. 获取排序范围并计算新的 rank 值
    const [prevRank, nextRank] = await this.boardItemRepository.getRankRange(
      parentBoardGroupId || null,
      rankAfter,
    );
    const rank = rankBetween(prevRank, nextRank);

    // 3. 检查是否已存在 BoardItem
    const existingBoardItem = await this.boardItemRepository.findByEntityId(entityType, entityId);

    let boardItem: BoardItem;

    if (existingBoardItem && existingBoardItem.boardId === boardId) {
      // 如果实体已经在同一个 board 中，更新位置
      if (parentBoardGroupId) {
        existingBoardItem.moveToBoardGroup(parentBoardGroupId, rank);
      } else {
        existingBoardItem.moveToRoot(rank);
      }
      await this.boardItemRepository.save(existingBoardItem);

      PutEntityToBoardHandler.logger.log(
        `Successfully updated position of ${entityType} ${entityId} in board ${boardId}`,
      );

      boardItem = existingBoardItem;
    } else {
      // 如果不存在或在不同的 board 中，创建新的 BoardItem
      boardItem = BoardItem.create(
        uuidv7(),
        boardId,
        entityType,
        entityId,
        parentBoardGroupId,
        rank,
      );

      await this.boardItemRepository.save(boardItem);

      PutEntityToBoardHandler.logger.log(
        `Successfully put ${entityType} ${entityId} to board ${boardId}`,
      );
    }

    // 通知 board 更新时间 (后台运行) TODO 通过 event 来实现
    runInBackground(this.commandBus.execute(new UpdateBoardUpdatedAtCommand(boardId)));

    return boardItem;
  }
}
