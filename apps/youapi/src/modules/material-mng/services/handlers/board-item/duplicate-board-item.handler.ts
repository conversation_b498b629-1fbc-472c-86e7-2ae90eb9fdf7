/**
 * Duplicate Board Item Handler - 复制 Board Item 处理器
 * 从 youapp 迁移的复制 Board Item 处理器
 *
 * 支持复制 Snip、Thought、BoardGroup 等各种类型的 Board Item
 * 复制后的项目会添加 " copy" 后缀，并保持原有的分组和排序关系
 *
 * Migrated from:
 * - /youapp/src/lib/app/board-item/index.ts (duplicateBoardItem function)
 */

import { Logger } from '@nestjs/common';
import { CommandBus, CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { AuthorizationService } from '@/modules/iam/services/authorization.service';
import { BoardItemRepository } from '@/modules/material-mng/repositories/board-item.repository';
import { EntityType } from '@/shared/db/public.schema';
import { BoardItemDto } from '../../../dto/board.dto';
import { BoardRepository } from '../../../repositories/board.repository';
import { BoardGroupRepository } from '../../../repositories/board-group.repository';
import { SnipRepository } from '../../../repositories/snip.repository';
import { ThoughtRepository } from '../../../repositories/thought.repository';
import { CreateBoardGroupCommand } from '../../commands/board-group/create-board-group.command';
import { DuplicateBoardItemCommand } from '../../commands/board-item/duplicate-board-item.command';
import { PutEntityToBoardCommand } from '../../commands/board-item/put-entity-to-board.command';
import { CloneSnipCommand } from '../../commands/snip/clone-snip.command';
import { UpdateSnipCommand } from '../../commands/snip/update-snip.command';
import { CloneThoughtCommand } from '../../commands/thought/clone-thought.command';
import { UpdateThoughtCommand } from '../../commands/thought/update-thought.command';
import { BoardDtoService } from '../../dto-services/board-dto.service';

@CommandHandler(DuplicateBoardItemCommand)
export class DuplicateBoardItemHandler
  implements ICommandHandler<DuplicateBoardItemCommand, BoardItemDto>
{
  private static readonly logger = new Logger(DuplicateBoardItemHandler.name);

  constructor(
    private readonly commandBus: CommandBus,
    private readonly authorizationService: AuthorizationService,
    private readonly boardRepository: BoardRepository,
    private readonly snipRepository: SnipRepository,
    private readonly thoughtRepository: ThoughtRepository,
    private readonly boardGroupRepository: BoardGroupRepository,
    private readonly boardDtoService: BoardDtoService,
    private readonly boardItemRepository: BoardItemRepository,
  ) {}

  async execute(command: DuplicateBoardItemCommand): Promise<BoardItemDto> {
    const { boardItemId, userId, spaceId } = command;

    DuplicateBoardItemHandler.logger.debug(`Duplicating board item ${boardItemId}`);

    // 鉴权，不存在则会抛异常
    const boardItem = await this.boardItemRepository.getById(boardItemId);
    const board = await this.boardRepository.getById(boardItem.boardId);
    this.authorizationService.validateSpacePermission(board.spaceId);

    // 1. 根据 board_item_id 查找对应的实体
    const { entityType, entityId, boardId, parentBoardGroupId } = boardItem;

    let clonedEntityId: string;
    let entity: any;

    // 3. 根据实体类型进行复制
    if (entityType === EntityType.SNIP) {
      // 复制 Snip
      const clonedSnip = await this.commandBus.execute(
        new CloneSnipCommand(entityId, spaceId, userId, boardId, parentBoardGroupId),
      );
      clonedEntityId = clonedSnip.id;

      // 修改标题，添加 " copy"
      await this.commandBus.execute(
        new UpdateSnipCommand(clonedEntityId, spaceId, userId, clonedSnip.title + ' copy'),
      );

      // 重新获取更新后的实体
      entity = await this.snipRepository.getById(clonedEntityId);
    } else if (entityType === EntityType.THOUGHT) {
      // 复制 Thought
      const clonedThought = await this.commandBus.execute(
        new CloneThoughtCommand(entityId, spaceId, userId, boardId, parentBoardGroupId),
      );
      clonedEntityId = clonedThought.id;

      // 修改标题，添加 " copy"
      await this.commandBus.execute(
        new UpdateThoughtCommand({
          thoughtId: clonedEntityId,
          title: clonedThought.title + ' copy',
        }),
      );

      // 重新获取更新后的实体
      entity = await this.thoughtRepository.getById(clonedEntityId);
    } else if (entityType === EntityType.BOARD_GROUP) {
      // 复制 BoardGroup
      const originalBoardGroup = await this.boardGroupRepository.getById(entityId);

      // 创建新的 BoardGroup
      const clonedBoardGroup = await this.commandBus.execute(
        new CreateBoardGroupCommand(
          userId,
          spaceId,
          boardId,
          originalBoardGroup.name + ' copy',
          originalBoardGroup.icon,
          originalBoardGroup.type,
        ),
      );
      clonedEntityId = clonedBoardGroup.id;

      // 递归复制组内的所有子项目
      await this.duplicateGroupChildren(entityId, clonedEntityId, boardId, userId, spaceId);

      entity = clonedBoardGroup;
    } else {
      throw new Error(`Unsupported entity type for duplication: ${entityType}`);
    }

    // 4. 将复制的实体添加到 board，排序在原来的 BoardItem 后面
    const newBoardItem = await this.commandBus.execute(
      new PutEntityToBoardCommand(
        boardId,
        entityType,
        clonedEntityId,
        parentBoardGroupId,
        boardItemId, // rankAfter: 排在原 boardItem 后面
      ),
    );

    DuplicateBoardItemHandler.logger.debug(
      `Board item duplication completed for ${clonedEntityId}`,
    );

    // 5. 将结果转换为 BoardItemDto 格式返回
    return await this.boardDtoService.toBoardItemDto(newBoardItem, entity);
  }

  /**
   * 递归复制 group 下的所有子项目
   */
  private async duplicateGroupChildren(
    originalGroupId: string,
    newGroupId: string,
    boardId: string,
    userId: string,
    spaceId: string,
  ): Promise<void> {
    DuplicateBoardItemHandler.logger.debug(
      `Duplicating children of group ${originalGroupId} to ${newGroupId}`,
    );

    // 获取原 group 下的所有 board item，已按 rank 排序
    const childrenBoardItems =
      await this.boardItemRepository.listByParentBoardGroupId(originalGroupId);

    if (childrenBoardItems.length === 0) {
      return;
    }

    // 递归复制 group 下的所有 board item，按顺序逐个复制以保持排序
    let lastInsertedBoardItemId: string | undefined;

    for (const childItem of childrenBoardItems) {
      let clonedEntityId: string;

      if (childItem.entityType === EntityType.SNIP) {
        // 复制 Snip
        const clonedSnip = await this.commandBus.execute(
          new CloneSnipCommand(childItem.entityId, spaceId, userId, boardId, newGroupId),
        );
        clonedEntityId = clonedSnip.id;
      } else if (childItem.entityType === EntityType.THOUGHT) {
        // 复制 Thought
        const clonedThought = await this.commandBus.execute(
          new CloneThoughtCommand(childItem.entityId, spaceId, userId, boardId, newGroupId),
        );
        clonedEntityId = clonedThought.id;
      } else {
        // 跳过不支持的实体类型
        DuplicateBoardItemHandler.logger.warn(
          `Skipping unsupported entity type: ${childItem.entityType}`,
        );
        continue;
      }

      // 将复制的实体添加到新的 group 下，排在上一个复制的项目后面
      const newBoardItem = await this.commandBus.execute(
        new PutEntityToBoardCommand(
          boardId,
          childItem.entityType,
          clonedEntityId,
          newGroupId, // parentBoardGroupId: 新的父组ID
          lastInsertedBoardItemId, // rankAfter: 排在上一个项目后面
        ),
      );
      lastInsertedBoardItemId = newBoardItem.id;
    }

    DuplicateBoardItemHandler.logger.debug(
      `Successfully duplicated ${childrenBoardItems.length} children from group ${originalGroupId} to ${newGroupId}`,
    );
  }
}
