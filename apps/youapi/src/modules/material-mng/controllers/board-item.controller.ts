import { Body, Controller, HttpCode, NotFoundException, Post } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { BaseController } from '@/shared/base.controller';
import { EntityType } from '@/shared/db/public.schema';
import { BoardItemDto } from '../dto/board.dto';
import { DuplicateBoardItemDto } from '../dto/board-item/duplicate-board-item.dto';
import { MoveBoardItemToBoardGroupDto } from '../dto/board-item/move-board-item-to-board-group.dto';
import { MoveBoardItemToRootDto } from '../dto/board-item/move-board-item-to-root.dto';
import { BoardGroupRepository } from '../repositories/board-group.repository';
import { SnipRepository } from '../repositories/snip.repository';
import { ThoughtRepository } from '../repositories/thought.repository';
import { DuplicateBoardItemCommand } from '../services/commands/board-item/duplicate-board-item.command';
import { MoveItemCommand } from '../services/commands/move/move-item.command';

/**
 * @deprecated 所有通过 api/v1/boardItem 提供的 API 均需要废弃，前端摒弃 boardItem 的概念，该用 position 来表示位置
 */
@ApiTags('Board Item')
@Controller('api/v1/boardItem')
export class BoardItemController extends BaseController {
  constructor(
    private readonly thoughtRepository: ThoughtRepository,
    private readonly boardGroupRepository: BoardGroupRepository,
    private readonly snipRepository: SnipRepository,
  ) {
    super();
  }

  /**
   * @deprecated 此 API 已废弃，但保持兼容性
   * 内部使用新的统一移动服务来实现
   */
  @Post('moveBoardItemToBoardGroup')
  @HttpCode(200)
  @ApiOperation({
    summary: '[DEPRECATED] Move BoardItem to BoardGroup',
    description:
      '此 API 已废弃但保持兼容，内部使用新的统一移动服务实现。建议迁移到 /api/v1/items/move',
  })
  @ApiResponse({
    status: 200,
    description: 'Success (using legacy compatibility)',
  })
  async moveBoardItemToBoardGroup(@Body() dto: MoveBoardItemToBoardGroupDto): Promise<void> {
    // 1. 根据 board_item_id 找到对应的实体
    const { entityType, entityId } = await this.findEntityByBoardItemId(dto.boardItemId);

    // 2. 使用新的统一移动命令
    const command = new MoveItemCommand(
      entityType,
      entityId,
      await this.getBoardIdFromBoardItem(dto.boardItemId), // 获取目标board
      this.getUserId(),
      dto.rankAfterId ? await this.findEntityByBoardItemId(dto.rankAfterId) : undefined, // rankAfter
      dto.parentBoardGroupId, // 移动到指定分组
    );

    await this.commandBus.execute(command);
  }

  /**
   * @deprecated 此 API 已废弃，但保持兼容性
   * 内部使用新的统一移动服务来实现
   */
  @Post('moveBoardItemToRoot')
  @HttpCode(200)
  @ApiOperation({
    summary: '[DEPRECATED] Move BoardItem to Root',
    description:
      '此 API 已废弃但保持兼容，内部使用新的统一移动服务实现。建议迁移到 /api/v1/items/move',
  })
  @ApiResponse({
    status: 200,
    description: 'Success (using legacy compatibility)',
  })
  async moveBoardItemToRoot(@Body() dto: MoveBoardItemToRootDto): Promise<void> {
    // 1. 根据 board_item_id 找到对应的实体
    const { entityType, entityId } = await this.findEntityByBoardItemId(dto.boardItemId);

    // 2. 获取目标board
    const targetBoardId = await this.getBoardIdFromBoardItem(dto.boardItemId);

    // 3. 处理 afterItem
    const rankAfter = dto.rankAfterId
      ? await this.findEntityByBoardItemId(dto.rankAfterId)
      : undefined;

    // 4. 使用新的统一移动命令
    const command = new MoveItemCommand(
      entityType,
      entityId,
      targetBoardId,
      this.getUserId(),
      rankAfter, // rankAfter
      undefined, // 移动到根层级（parentBoardGroupId = undefined）
    );

    await this.commandBus.execute(command);
  }

  /**
   * @deprecated 此 API 已废弃，但保持兼容性
   * 复制一个 BoardItem（Snip/Thought/BoardGroup）
   */
  @Post('duplicate')
  @HttpCode(200)
  @ApiOperation({
    summary: '[DEPRECATED] Duplicate BoardItem',
    description: '此 API 已废弃但保持兼容。复制一个 BoardItem，支持 Snip、Thought、BoardGroup 类型',
  })
  @ApiResponse({
    status: 200,
    description: 'Success - returns duplicated BoardItemDto',
    type: BoardItemDto,
  })
  async duplicate(@Body() dto: DuplicateBoardItemDto): Promise<BoardItemDto> {
    const command = new DuplicateBoardItemCommand(
      dto.boardItemId,
      this.getUserId(),
      await this.getSpaceId(),
    );

    return await this.commandBus.execute(command);
  }

  /**
   * 根据 board_item_id 查找对应的实体类型和实体ID
   * 这是兼容旧API的关键方法
   */
  private async findEntityByBoardItemId(
    boardItemId: string,
  ): Promise<{ entityType: EntityType; entityId: string }> {
    // 首先尝试在 thought 中查找
    const thought = await this.thoughtRepository.findByBoardItemId(boardItemId);
    if (thought) {
      return { entityType: 'thought', entityId: thought.id };
    }

    // 然后尝试在 board_group 中查找
    const boardGroup = await this.boardGroupRepository.findByBoardItemId(boardItemId);
    if (boardGroup) {
      return { entityType: 'board_group', entityId: boardGroup.id };
    }

    // 尝试在 snip 中查找
    const snip = await this.snipRepository.findByBoardItemId(boardItemId);
    if (snip) {
      return { entityType: 'snip', entityId: snip.id };
    }

    throw new NotFoundException(`No entity found for board_item_id: ${boardItemId}`);
  }

  /**
   * 根据 board_item_id 获取对应的 board_id
   */
  private async getBoardIdFromBoardItem(boardItemId: string): Promise<string> {
    // 通过任意实体的位置信息获取 boardId
    const { entityType, entityId } = await this.findEntityByBoardItemId(boardItemId);

    if (entityType === 'thought') {
      const thought = await this.thoughtRepository.getById(entityId);
      return thought.position.boardId;
    }

    if (entityType === 'board_group') {
      const boardGroup = await this.boardGroupRepository.getById(entityId);
      if (!boardGroup.boardId) {
        throw new Error(`BoardGroup ${entityId} is not associated with any board`);
      }
      return boardGroup.boardId;
    }

    if (entityType === 'snip') {
      const snip = await this.snipRepository.getById(entityId);
      if (!snip.position || !snip.position.boardId) {
        throw new Error(`Snip ${entityId} is not associated with any board`);
      }
      return snip.position.boardId;
    }

    throw new Error(`Unsupported entity type: ${entityType}`);
  }
}
