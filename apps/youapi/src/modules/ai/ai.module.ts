import { Module } from '@nestjs/common';
import { DiscoveryModule } from '@nestjs/core';
import { CqrsModule } from '@nestjs/cqrs';
import { CommonModule } from '@/common/common.module';
import { DomainModule } from '@/domain/domain.module';
import { InfraModule } from '@/infra/infra.module';
import { ContextsModule } from './contexts/contexts.module';
import { AIController } from './controllers/ai.controller';
import { PromptService } from './prompt/index.service';
import { ModelProviderService } from './providers/index.service';
import { CompletionBlockRepository } from './repositories/completion-block.repository';
import { GenerationRepository } from './repositories/generation.repository';
import { ObjectRunnerService, TextRunnerService } from './runners';
import { ExplainService } from './services/explain.service';
import { UpdateCompletionBlockHandler } from './services/handlers/update-completion-block.handler';
import { ImageService } from './services/image.service';
import { OverviewService } from './services/overview.service';
import { SelfDescService } from './services/self-desc.service';
import { SimpleSummaryService } from './services/simple-summary.service';
import { SpeechService } from './services/speech.service';
import { SuggestionService } from './services/suggestion.service';
import { TingwuService } from './services/tingwu.service';
// Import all tool services directly
import { AudioGenerateService } from './tools/audio-generate.tool';
import { BoardSearchService } from './tools/board-search.tool';
import { CreateBoardService } from './tools/create-board.tool';
import { CreateSnipByUrlService } from './tools/create-snip-by-url.tool';
import { DiagramGenerateService } from './tools/diagram-generate.tool';
import { EditThoughtService } from './tools/edit-thought.tool';
import { GoogleSearchService } from './tools/google-search.tool';
import { ImageGenerateService } from './tools/image-generate.tool';
import { LibrarySearchService } from './tools/library-search.tool';
import { OrganizeDirectoryStructureService } from './tools/organize-directory-structure.tool';
import { PrefetchUrlsService } from './tools/prefetch-urls.tool';
import { ToolCallService } from './tools/tool-call.service';
import { YoutubeFactoryService } from './tools/youtube-factory.tool';

@Module({
  imports: [CommonModule, DomainModule, InfraModule, CqrsModule, DiscoveryModule, ContextsModule],
  controllers: [AIController],
  providers: [
    // Core AI services
    TextRunnerService,
    ObjectRunnerService,
    ExplainService,
    SimpleSummaryService,
    ImageService,
    SpeechService,
    OverviewService,
    ModelProviderService,
    PromptService,
    TingwuService,
    SelfDescService,
    SuggestionService,

    // Tool services (previously in ToolsModule)
    AudioGenerateService,
    ImageGenerateService,
    DiagramGenerateService,
    CreateBoardService,
    BoardSearchService,
    GoogleSearchService,
    LibrarySearchService,
    EditThoughtService,
    YoutubeFactoryService,
    OrganizeDirectoryStructureService,
    CreateSnipByUrlService,
    PrefetchUrlsService,
    ToolCallService,

    // Repositories
    CompletionBlockRepository,
    GenerationRepository,

    // Command Handlers
    UpdateCompletionBlockHandler,
  ],
  exports: [
    // Core AI services
    TextRunnerService,
    ObjectRunnerService,
    ExplainService,
    SimpleSummaryService,
    ImageService,
    SpeechService,
    OverviewService,
    ModelProviderService,
    PromptService,
    TingwuService,
    SelfDescService,

    // Tool services
    ToolCallService,

    // Repositories
    GenerationRepository,
  ],
})
export class AiModule {}
