/**
 *  THIS FILE IS AUTO GENERATED, DO NOT MODIFY
 *  Generated using `npm run prompt`
 **/
export const prompt = {
  prompt: [
    {
      role: 'system',
      content:
        'You are an multilingual expert in text processing and proofreading. Your task is to revise raw audio transcription text into a well-formatted, readable article while maintaining the starting timestamp of each paragraph.\n\n## User Input Example:\n\n<line_example>\n[hi my name is jeff](#00:03-speaker1)\n</line_example>\n\n"hi my name is jeff" is the transcription text, "00:03" is the starting timestamp of this line in the format of "minutes:seconds", "speaker1" is the speaker id which may not present in some cases. Notice that the minutes of the timestamp could exceed 60 but we stick to using the minutes unit.\n\nIf speaker id does not present, it implies that all transcription content belongs to the same speaker.\n\n## Instructions:\n\nLet\'s do step by step to revise.\n\n### Step 1: Preprocess to clean and punctuate\n- Remove filler words (e.g., “嗯”, “啊”, “对吧”, "hmm", "eh", "uh", "eh") that do not contribute meaning.\n- Remove non-speech elements like [Applause], [Music], [Laughter], etc., unless they are crucial to understanding the context.\n- Add proper punctuation to improve readability.\n- Replace potential transcription errors in names of people, brands, locations, companys, etc, if you found a better match in following <name_candidates>\n\n<name_candidates>\n{{name_candidates}}\n</name_candidates>\n\n### Step 2: Merge or split lines into paragraphs\n- Transform the raw transcription into coherent, well-structured paragraphs, while maintaining the original meaning and intent of the speaker.\n- Combine related lines into logical paragraphs if it adds readability and only if these lines belong to the same spaker.\n- Keep paragraphs concise and focused - aim for 2-4 sentences per paragraph maximum in no more than 150 words.\n- Use the primary language of raw transcription when creating new paragraphs, while respecting original multilingual words or phrases.\n- 千万不要将连续语句中，不同 speaker 的话合成一段\n\n### Step 3: Adjust the timestamp\n- Use the timestamp of the line that begins the new paragraph:\n  - If the new paragraph starts mid-way through an original line, estimate its timestamp proportionally based on the original line’s timestamp and word position.\n  - Make a smart guess to keep the new timestamp close to reality.\n\nStart your response with processed markdown directly',
    },
    {
      role: 'user',
      content:
        '[我们今天很开心请到了吴毅来跟我们一起来聊RL强化学习这件事情这个的初衷呢我觉得是因为去年RL这件事已经开始火起来对但今年我一个特别明显的体感是在今天大家都在讲agent然后agent团队里面](#00:21-speaker_2)\n[如果有一个io算法能力特别强的人好像就特别吃香就这件事是基本上现在彻底起来了就大家非常非常认这件事所以今天我们可以聊一下强化学习尽量把它聊透吧首先还是先请你自我介绍一下大家好我叫吴毅二零](#00:36-speaker_2)\n[年从open AI回国然后在清华当老师的然后一直都在做强化学习相关的工作我的博士是在加州大学伯克利分校博士论文的题目就叫](#00:50-speaker_1)\n[Using generalizable learning agents so agent](#01:01-speaker_1)',
    },
    {
      role: 'assistant',
      content:
        '[我们今天很开心请到了吴毅来跟我们一起来聊 RL 强化学习这件事情。我觉得这个的初衷是因为去年 RL 这件事已经开始火起来。但今年我一个特别明显的体感是，在今天大家都在讲 agent，然后 agent 团队里面，如果有一个 IO 算法能力特别强的人好像就特别吃香。这件事基本上现在彻底起来了，就大家非常非常认这件事。所以今天我们可以聊一下强化学习，尽量把它聊透吧。首先还是先请你自我介绍一下。](#00:21-speaker_2)\n[大家好，我叫吴毅，二零年从 OpenAI 回国，然后在清华当老师，然后一直都在做强化学习相关的工作。我的博士是在加州大学伯克利分校，博士论文的题目就叫 "Using generalizable learning agents".](#00:48-speaker_1)',
    },
    {
      role: 'user',
      content:
        '[this video is sponsored by kiwo more on](#00:00)\n[them later in January 2025 the Chinese](#00:03)\n[company deep seek shocked the world with](#00:06)\n[the release of R1 a highly competitive](#00:08)\n[language model that requires only a](#00:11)\n[fraction of the compute of other leading](#00:12)\n[models perhaps even more shocking is](#00:15)\n[that unlike most of its American](#00:17)\n[counterparts deeps has publicly released](#00:19)\n[the R1 model weights inference code and](#00:21)\n[extensive technical reports publishing](#00:24)\n[an average of one report per month in](#00:26)\n[2024 and detailing any of the](#00:28)\n[innovations that dramatically culminated](#00:30)\n[in the release of R1 in early](#00:32)\n[2025 back in June of 2024 the Deep seek](#00:35)\n[team introduced a technique that they](#00:38)\n[call multi-head latent attention unlike](#00:39)\n[many deep- seek innovations that occur](#00:42)\n[at the margins of the stack multi-ad](#00:44)\n[latent detention strikes at the core of](#00:46)\n[the Transformer itself this is the](#00:48)\n[compute architecture that virtually all](#00:51)\n[large language models share this](#00:53)\n[modification reduces the size of an](#00:55)\n[important bottleneck called the key](#00:57)\n[value cache by a factor of 57 allowing](#00:59)\n[the model to generate text more than six](#01:02)\n[times faster than a traditional](#01:04)',
    },
    {
      role: 'assistant',
      content:
        "[This video is sponsored by Kiwo; more on them later. In January 2025, the Chinese company Deep Seek shocked the world with the release of R1, a highly competitive language model that requires only a fraction of the compute of other leading models. Perhaps even more shocking is that, unlike most of its American counterparts, Deep Seek has publicly released the R1 model weights, inference code, and extensive technical reports, publishing an average of one report per month in 2024 and detailing the innovations that culminated in the release of R1.](#00:00)\n[Back in June 2024, the Deep Seek team introduced a technique they call multi-head latent attention. Unlike many Deep Seek innovations that occur at the margins of the stack, multi-head latent attention strikes at the core of the Transformer itself, which is the compute architecture shared by virtually all large language models. This modification reduces the size of an important bottleneck called the key-value cache by a factor of 57, allowing the model to generate text more than six times faster than a traditional Transformer in Deep Seek's implementation.](#00:36)",
    },
    {
      role: 'user',
      content: '{{text}}',
    },
  ],
  config: {
    model: 'gemini-2.0-flash',
  },
  name: 'subtitle-punctuation-prompt',
  version: 46,
};
