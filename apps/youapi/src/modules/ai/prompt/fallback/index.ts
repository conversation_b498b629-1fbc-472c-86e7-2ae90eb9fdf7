/* eslint-disable prettier/prettier */
/**
 *  THIS FILE IS AUTO GENERATED, DO NOT MODIFY
 *  Generated using `npm run prompt`
 **/
export { prompt as aiaskchatprompt } from './ai-ask-chat-prompt';
export { prompt as audiooverviewprompt } from './audio-overview-prompt';
export { prompt as boardcreateprompt } from './board-create-prompt';
export { prompt as buildobjectiveprompt } from './build-objective-prompt';
// contexts
export * from './contexts';
export { prompt as createboardprompt } from './create-board-prompt';
export { prompt as createquizprompt } from './create-quiz-prompt';
export { prompt as customassistantprompt } from './custom-assistant-prompt';
export { prompt as customassistantprompt2 } from './custom-assistant-prompt2';
export { prompt as deepsearchprompt } from './deep-search-prompt';
export { prompt as detectlanguage } from './detect-language';
export { prompt as detectspeakersbytranscript } from './detect-speakers-by-transcript';
export { prompt as dongdongaiwritterdemoprompt } from './dongdong-ai-writter-demo-prompt';
export { prompt as explainimageprompt } from './explain-image-prompt';
export { prompt as extractentitynames } from './extract-entity-names';
export { prompt as generateboarddraftprompt } from './generate-board-draft-prompt';
export { prompt as generateboardintroprompt } from './generate-board-intro-prompt';
export { prompt as generatechatessenceprompt } from './generate-chat-essence-prompt';
export { prompt as generateimageinfoprompt } from './generate-image-info-prompt';
export { prompt as generatemindmap } from './generate-mindmap';
export { prompt as generatetitleprompt } from './generate-title-prompt';
export { prompt as getselfdescbyfeeds } from './get-self-desc-by-feeds';
export { prompt as infersearchqueries } from './infer-search-queries';
export { prompt as instantapplyprompt } from './instant-apply-prompt';
export { prompt as magiccreationoutlineprompt } from './magic-creation-outline-prompt';
export { prompt as magiccreationsummaryprompt } from './magic-creation-summary-prompt';
export { prompt as mergesubtaskprompt } from './merge-subtask-prompt';
export { prompt as newassistantpreviewprompt } from './new-assistant-preview-prompt';
export { prompt as notsomagicnewboard } from './not-so-magic-new-board';
export { prompt as organizeboardprompt } from './organize-board-prompt';
export { prompt as overviewarticleprompt } from './overview-article-prompt';
export { prompt as overviewvideomultispeakerprompt } from './overview-video-multi-speaker-prompt';
export { prompt as overviewvideoprompt } from './overview-video-prompt';
export { prompt as prefetchaurlprompt } from './prefetch-a-url-prompt';
export { prompt as queryanalyzeprompt } from './query-analyze-prompt';
export { prompt as queryrewriteprompt } from './query-rewrite-prompt';
export { prompt as quotegeneratorprompt } from './quote-generator-prompt';
export { prompt as runsubtaskprompt } from './run-subtask-prompt';
export { prompt as searchaiprompt } from './search-ai-prompt';
export { prompt as shutingsvgtemplate } from './shuting-svg-template';
export { prompt as simpleexplaintextprompt } from './simple-explain-text-prompt';
export { prompt as simplesummaryofacontent } from './simple-summary-of-a-content';
export { prompt as subtitlepunctuationprompt } from './subtitle-punctuation-prompt';
export { prompt as suggestionprompt } from './suggestion-prompt';
export { prompt as svggeneratorprompt } from './svg-generator-prompt';
export { prompt as svgoptimizerprompt } from './svg-optimizer-prompt';
export { prompt as thoughtautogeneratetitleprompt } from './thought-auto-generate-title-prompt';
export { prompt as translateprompt } from './translate-prompt';
export { prompt as translatetranscriptprompt } from './translate-transcript-prompt';
// user prompt
export * from './user-prompt';
export { prompt as writersystemprompt } from './writer-system-prompt';
export { prompt as writersystempromptjialiangagent } from './writer-system-prompt-jialiang-agent';
export { prompt as youtubeboardsnipprompt } from './youtube-board-snip-prompt';
export { prompt as youtubecoreclipsprompt } from './youtube-core-clips-prompt';
export { prompt as youtubefactoryprompt } from './youtube-factory-prompt';
