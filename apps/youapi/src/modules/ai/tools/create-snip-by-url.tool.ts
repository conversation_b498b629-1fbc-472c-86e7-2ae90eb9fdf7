/**
 * Create Snip By URL Tool Service - 通过URL创建片段工具服务
 * 从URL创建不同类型的片段（网页、PDF、图片等）
 *
 * Migrated from:
 * - youapp/src/domain/chat/tool_call/create_snip_by_url.ts
 * - apps/youapi/src/domain/chat/tool_call/create-snip-by-url.service.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { LangfuseSpanClient } from 'langfuse-core';
import { Subject } from 'rxjs';
import z from 'zod';
import { InvalidArguments } from '@/common/errors';
import type { CompletionStreamChunk, StreamChunkUnion } from '@/common/types';
import {
  CompletionStreamModeEnum,
  type CompletionStreamReplaceChunk,
  ToolNames,
} from '@/common/types';
import { SnipStatusEnum } from '@/common/types/snip.types';
import type { ToolCallResult, ToolFunctionParameters } from '@/common/types/tool-call.types';
import { sleep } from '@/common/utils';
import { SnipTypeEnum } from '@/domain/snip/types';
import { GetSpaceByUserIdQuery } from '@/modules/iam/services/queries/space/get-space-by-user-id.query';
import { CloneSnipCommand } from '@/modules/material-mng/services/commands/snip/clone-snip.command';
import { TryCreateSnipByUrlCommand } from '@/modules/material-mng/services/commands/snip/try-create-snip-by-url.command';
import { CloneThoughtCommand } from '@/modules/material-mng/services/commands/thought/clone-thought.command';
import { GetSnipsQuery } from '@/modules/material-mng/services/queries/snip/get-snips.query';
import { BaseToolService } from './base.service';
import { Tool } from './decorators/tool-register.decorator';

// 定义通过URL创建片段参数的类型
const CreateSnipByUrlParameters = z.object({
  urls: z.array(z.string()).describe('A list of resource URLs to create Snips from'),
  titles: z.array(z.string()).optional().describe('A list of resource titles to create Snips from'),
  album_urls: z
    .array(z.string())
    .optional()
    .describe(
      'A list of album cover URLs for audio/video Snips. This should be used with audio URLs generated from audio_generate tool - use the album_url from audio generation result. Each album URL should correspond to the URL at the same index in the urls array.',
    ),
  sync: z
    .boolean()
    .optional()
    .default(false)
    .describe(
      'If true, wait for all snips to finish fetching (up to 2 minutes). If false (default), return immediately after creating snips.',
    ),
});

type CreateSnipByUrlParameters = z.infer<typeof CreateSnipByUrlParameters>;

const MessageCreateSingleUrlResultSchema = z.object({
  url: z.string(),
  success: z.boolean(),
  error: z.string().nullable(),
  board_id: z.string().nullable(),
  snip: z.any().nullable(),
  type: z.string().optional(),
  provided_title: z.string().optional(),
  provided_album_url: z.string().optional(),
});

const CreateSnipByUrlDescription = `Save webpage to current board, accept one or more URLs of webpages, PDFs, and images. Tool response includes the result of this action. Should be called only when the user explicitly asks to create or save materials.`;

@Tool({
  name: ToolNames.CREATE_SNIP_BY_URL,
  description: CreateSnipByUrlDescription,
  parameters: CreateSnipByUrlParameters,
  timeout: 300000, // 5 minutes for URL processing
  maxCalls: 8,
})
@Injectable()
export class CreateSnipByUrlService extends BaseToolService {
  private readonly logger = new Logger(CreateSnipByUrlService.name);

  async execute(
    parameters: ToolFunctionParameters,
    span: LangfuseSpanClient,
    subject: Subject<CompletionStreamChunk<StreamChunkUnion>>,
  ): Promise<ToolCallResult> {
    const { userId, parsedParams, chat, completionBlock } = parameters;
    const {
      urls, // 注意：这里从 parsed_params 中获取的是 urls
      titles: unSafeTitles,
      album_urls: unSafeAlbumUrls,
      sync = false, // 默认为 false
    } = parsedParams;
    const common_board_id = chat.getBoardId();

    if (!urls || !Array.isArray(urls) || urls.length === 0) {
      console.error('urls are not provided or empty', chat.id);
      throw new InvalidArguments('missing or empty urls array');
    }

    let titles: string[] = unSafeTitles as unknown as string[];

    // 验证 titles 参数：如果提供了 titles，数量必须与 urls 匹配
    if (titles && Array.isArray(titles) && titles.length !== urls.length) {
      console.error('titles array length does not match urls array length', chat.id);
      titles = [];
    }

    let album_urls: string[] = unSafeAlbumUrls as unknown as string[];

    // 验证 album_urls 参数：如果提供了 album_urls，数量必须与 urls 匹配
    if (album_urls && Array.isArray(album_urls) && album_urls.length !== urls.length) {
      console.error('album_urls array length does not match urls array length', chat.id);
      album_urls = [];
    }

    if (!common_board_id) {
      throw new InvalidArguments('missing board_id');
    }

    // 初始化空数组以存储结果
    subject.next({
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'CompletionBlock',
      targetId: completionBlock.id,
      data: [],
      path: 'tool_result.snipsResults',
    } as CompletionStreamReplaceChunk<StreamChunkUnion>);

    const results: z.infer<typeof MessageCreateSingleUrlResultSchema>[] = [];
    let successful_creations = 0;
    let failed_creations = 0;

    for (const [index, url] of urls.entries()) {
      const title = titles && Array.isArray(titles) ? titles[index] : undefined;
      const album_url = album_urls && Array.isArray(album_urls) ? album_urls[index] : undefined;

      if (!url || typeof url !== 'string') {
        // 无效的URL条目
        console.warn('Invalid URL entry in the list:', url, chat.id);
        const invalidResult = {
          url: String(url) || 'invalid_url_entry',
          success: false,
          error: 'Invalid URL entry provided in the list.',
          board_id: null,
          snip: null,
          provided_title: title?.trim() ? title.trim() : undefined,
          provided_album_url: album_url?.trim() ? album_url.trim() : undefined,
        };

        results.push(invalidResult);

        // subject.next 实时更新无效URL的结果
        subject.next({
          mode: CompletionStreamModeEnum.REPLACE,
          targetType: 'CompletionBlock',
          targetId: completionBlock.id,
          data: invalidResult,
          path: `tool_result.snipsResults[${index}]`,
        } as CompletionStreamReplaceChunk<StreamChunkUnion>);

        failed_creations++;
        continue;
      }

      // 为每个URL创建一个子 span
      const childSpan = span.span({
        name: 'create-single-snip-by-url',
        input: {
          url,
          user_id: userId,
          board_id: common_board_id,
        },
      });

      try {
        // subject.next 当前正在处理的URL的状态
        subject.next({
          mode: CompletionStreamModeEnum.REPLACE,
          targetType: 'CompletionBlock',
          targetId: completionBlock.id,
          data: {
            status: 'processing',
            current_url: url,
            current_index: index,
            total: urls.length,
          },
          path: 'tool_result.progress',
        } as CompletionStreamReplaceChunk<StreamChunkUnion>);

        // 检查是否是 YouMind URL
        const youMindUrl = this.checkYouMindUrl(url);
        if (youMindUrl) {
          const itemId = this.extractYouMindItemIdFromUrl(url, youMindUrl.type);
          childSpan.event({
            name: 'youmind-item-url-detected',
            input: { url, user_id: userId, item_id: itemId, type: youMindUrl.type },
          });

          if (!itemId) {
            const invalidSnipResult = {
              url,
              success: false,
              error: 'Invalid YouMind Snip URL: could not extract Snip ID',
              board_id: common_board_id,
              snip: null,
              type: youMindUrl.type,
              provided_title: title?.trim() ? title.trim() : undefined,
              provided_album_url: album_url?.trim() ? album_url.trim() : undefined,
            };

            results.push(invalidSnipResult);

            subject.next({
              mode: CompletionStreamModeEnum.REPLACE,
              targetType: 'CompletionBlock',
              targetId: completionBlock.id,
              data: invalidSnipResult,
              path: `tool_result.snipsResults[${index}]`,
            } as CompletionStreamReplaceChunk<StreamChunkUnion>);

            failed_creations++;
            childSpan.end();
            continue;
          }

          // 调用特殊处理逻辑
          const handleResult = await this.handleYouMindItemUrl(
            itemId,
            common_board_id,
            youMindUrl.type,
            userId,
          );

          const specialResult = {
            url,
            success: handleResult.success,
            error: handleResult.error || null,
            board_id: common_board_id,
            snip: handleResult.snip || null,
            type: youMindUrl.type,
            provided_title: title?.trim() ? title.trim() : undefined,
            provided_album_url: album_url?.trim() ? album_url.trim() : undefined,
          };

          results.push(specialResult);

          // subject.next 实时更新特殊处理的结果
          subject.next({
            mode: CompletionStreamModeEnum.REPLACE,
            targetType: 'CompletionBlock',
            targetId: completionBlock.id,
            data: specialResult,
            path: `tool_result.snipsResults[${index}]`,
          } as CompletionStreamReplaceChunk<StreamChunkUnion>);

          if (handleResult.success) {
            successful_creations++;
            childSpan.event({
              name: 'youmind-item-url-processed-successfully',
              input: { url, user_id: userId, item_id: itemId },
              output: handleResult.snip,
            });
          } else {
            failed_creations++;
            childSpan.event({
              name: 'youmind-item-url-processing-failed',
              input: { url, user_id: userId, item_id: itemId, error: handleResult.error },
            });
          }

          childSpan.end();
          continue;
        }

        // Get spaceId from userId using material-mng method
        const space = await this.queryBus.execute(new GetSpaceByUserIdQuery(userId));
        const spaceId = space.id;

        // Create snip by URL using material-mng command
        const command = new TryCreateSnipByUrlCommand(
          spaceId,
          userId,
          url,
          common_board_id,
          undefined, // parentBoardGroupId
          title,
          album_url, // heroImageUrl
        );

        const result = await this.commandBus.execute(command);

        if (!result) {
          childSpan.event({
            name: 'no-snip-created-for-url',
            input: { url, user_id: userId },
          });
          // 无法处理此 URL
          const failResult = {
            url,
            success: false,
            error:
              'Cannot process this URL. Please ensure the URL points to a valid resource (webpage, PDF, image, etc.)',
            board_id: common_board_id,
            snip: null,
            provided_title: title?.trim() ? title.trim() : undefined,
            provided_album_url: album_url?.trim() ? album_url.trim() : undefined,
          };

          results.push(failResult);

          // subject.next 实时更新失败的结果
          subject.next({
            mode: CompletionStreamModeEnum.REPLACE,
            targetType: 'CompletionBlock',
            targetId: completionBlock.id,
            data: failResult,
            path: `tool_result.snipsResults[${index}]`,
          } as CompletionStreamReplaceChunk<StreamChunkUnion>);

          failed_creations++;
        } else {
          childSpan.event({
            name: 'snip-created-for-url',
            input: { url, user_id: userId, snip_id: result.id },
            output: result,
          });
          // Snip 创建成功
          const successResult = {
            url,
            success: true,
            board_id:
              common_board_id ||
              (result.board_ids && result.board_ids.length > 0 ? result.board_ids[0] : null), // 如果没有提供 common_board_id, 则使用 snip 返回的第一个 board_id
            snip: result,
            snip_predicted_type: this.predictSnipType(url),
            error: null,
            type: 'snip' as const,
            // 添加用户提供的标题，如果有的话
            provided_title: title?.trim() ? title.trim() : undefined,
            provided_album_url: album_url?.trim() ? album_url.trim() : undefined,
          };

          results.push(successResult);

          // subject.next 实时更新成功的结果
          subject.next({
            mode: CompletionStreamModeEnum.REPLACE,
            targetType: 'CompletionBlock',
            targetId: completionBlock.id,
            data: successResult,
            path: `tool_result.snipsResults[${index}]`,
          } as CompletionStreamReplaceChunk<StreamChunkUnion>);

          successful_creations++;
        }
        childSpan.end();
      } catch (error) {
        // 创建 Snip 失败
        console.error(`Failed to create Snip by URL: ${url}`, error);
        childSpan.event({
          name: 'create-snip-by-url-failed-for-url',
          input: {
            url,
            user_id: userId,
            error: error instanceof Error ? (error as any)?.name : String(error),
          },
        });
        childSpan.end({
          level: 'ERROR' as any,
          statusMessage: (error as Error).message,
        });
        // 创建 Snip 失败：错误信息
        const errorResult = {
          url,
          success: false,
          error: `Failed to create Snip: ${(error as any)?.name || 'Unknown error'}  ${(error as any).message}`,
          board_id: common_board_id,
          snip: null,
          provided_title: title?.trim() ? title.trim() : undefined,
          provided_album_url: album_url?.trim() ? album_url.trim() : undefined,
        };

        results.push(errorResult);

        // subject.next 实时更新失败的结果
        subject.next({
          mode: CompletionStreamModeEnum.REPLACE,
          targetType: 'CompletionBlock',
          targetId: completionBlock.id,
          data: errorResult,
          path: `tool_result.snipsResults[${index}]`,
        } as CompletionStreamReplaceChunk<StreamChunkUnion>);

        failed_creations++;
      }

      // 更新当前处理进度
      subject.next({
        mode: CompletionStreamModeEnum.REPLACE,
        targetType: 'CompletionBlock',
        targetId: completionBlock.id,
        data: {
          processed: index + 1,
          total: urls.length,
          successful: successful_creations,
          failed: failed_creations,
        },
        path: 'tool_result.stats',
      } as CompletionStreamReplaceChunk<StreamChunkUnion>);
    }

    let summary_message = '';
    if (urls.length === 1) {
      if (successful_creations === 1) {
        // 成功创建1个Snip。
        summary_message = `Successfully created 1 Snip.`;
      } else {
        // 创建Snip失败。
        summary_message = results[0].error || `Failed to create Snip.`;
      }
    } else {
      // 共尝试创建 N 个 Snip。成功 M 个，失败 P 个。
      summary_message = `Attempted to create ${urls.length} Snips. ${successful_creations} succeeded, ${failed_creations} failed.`;
    }

    // 如果启用了同步模式，等待所有成功创建的 snips 完成抓取
    if (sync && successful_creations > 0) {
      this.logger.log(
        `Sync mode enabled, waiting for ${successful_creations} snips to complete fetching`,
      );

      // 收集成功创建的 snip IDs
      const successfulSnipIds = results
        .filter((r) => r.success && r.snip?.id)
        .map((r) => r.snip.id);

      if (successfulSnipIds.length > 0) {
        // 获取 spaceId
        const space = await this.queryBus.execute(new GetSpaceByUserIdQuery(userId));
        const spaceId = space.id;

        // 创建一个子 span 来跟踪等待过程
        const waitSpan = span.span({
          name: 'wait-for-snips-completion',
          input: {
            snip_ids: successfulSnipIds,
            sync: true,
          },
        });

        try {
          // 等待 snips 完成抓取
          const completedSnips = await this.waitForSnipsToComplete(
            successfulSnipIds,
            spaceId,
            userId,
            120000, // 2 分钟超时
            waitSpan,
          );

          // 更新 results 中的 snip 信息
          for (const result of results) {
            if (result.success && result.snip?.id) {
              const updatedSnip = completedSnips.find((s: any) => s.id === result.snip.id);
              if (updatedSnip) {
                result.snip = updatedSnip;
              }
            }
          }

          // 通知前端 snips 已完成抓取
          subject.next({
            mode: CompletionStreamModeEnum.REPLACE,
            targetType: 'CompletionBlock',
            targetId: completionBlock.id,
            data: {
              fetching_completed: true,
              completed_count: completedSnips.length,
            },
            path: 'tool_result.fetching_status',
          } as CompletionStreamReplaceChunk<StreamChunkUnion>);

          this.logger.log(`Sync wait completed, ${completedSnips.length} snips fetched`);
        } catch (error) {
          this.logger.error('Error during sync wait:', error);
        } finally {
          waitSpan.end();
        }
      }
    }

    // 最终更新摘要信息
    subject.next({
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'CompletionBlock',
      targetId: completionBlock.id,
      data: summary_message,
      path: 'tool_result.summary_message',
    } as CompletionStreamReplaceChunk<StreamChunkUnion>);

    return {
      response: summary_message, // 提供给 LLM 的自然语言响应
      result: {
        // 结构化数据，符合 tool_output_schema
        snipsResults: results,
        summary_message,
        sync_completed: sync, // 标记是否执行了同步等待
      },
    };
  }

  private predictSnipType(
    url: string,
  ): SnipTypeEnum.ARTICLE | SnipTypeEnum.VOICE | SnipTypeEnum.VIDEO {
    if (url.includes('youtube.com') || url.includes('youtu.be')) {
      return SnipTypeEnum.VIDEO;
    }
    if (url.includes('xiaoyuzhoufm.com') || url.includes('podcasts.apple.com')) {
      return SnipTypeEnum.VOICE;
    }
    return SnipTypeEnum.ARTICLE;
  }

  // Check if URL is a YouMind URL
  private checkYouMindUrl(url: string): {
    type: 'snip' | 'thought';
  } | null {
    try {
      const urlObj = new URL(url);
      const youmindDomains = ['youmind.ai'];

      const isValidDomain = youmindDomains.some(
        (domain) => urlObj.hostname === domain || urlObj.host === domain,
      );
      if (!isValidDomain) {
        return null;
      }

      const isSnip =
        /^\/snips\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
          urlObj.pathname,
        );
      if (isSnip) {
        return {
          type: 'snip',
        };
      }

      const isThought =
        /^\/thoughts\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
          urlObj.pathname,
        );
      if (isThought) {
        return {
          type: 'thought',
        };
      }
      return null;
    } catch {
      return null;
    }
  }

  // Extract item ID from YouMind URL
  private extractYouMindItemIdFromUrl(url: string, type: 'snip' | 'thought'): string | null {
    try {
      const urlObj = new URL(url);
      const match =
        type === 'snip'
          ? urlObj.pathname.match(
              /^\/snips\/([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})$/i,
            )
          : urlObj.pathname.match(
              /^\/thoughts\/([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})$/i,
            );
      return match ? match[1] : null;
    } catch {
      return null;
    }
  }

  private async handleYouMindItemUrl(
    itemId: string,
    boardId: string,
    type: 'snip' | 'thought',
    userId: string,
  ): Promise<{
    success: boolean;
    error?: string;
    snip?: any;
  }> {
    if (type === 'snip') {
      const command = new CloneSnipCommand(itemId, userId, boardId);

      try {
        const newSnip = await this.commandBus.execute(command);
        return {
          success: true,
          snip: newSnip,
        };
      } catch (error) {
        return {
          success: false,
          error: 'Snip not found or cannot be cloned',
        };
      }
    }

    if (type === 'thought') {
      const command = new CloneThoughtCommand(itemId, userId, boardId);

      try {
        const newThought = await this.commandBus.execute(command);
        return {
          success: true,
          snip: newThought,
        };
      } catch (error) {
        return {
          success: false,
          error: 'Thought not found or cannot be cloned',
        };
      }
    }

    return {
      success: false,
      error: 'Invalid YouMind item type',
    };
  }

  /**
   * 等待 snips 完成抓取
   * @param snipIds - snip ID 列表
   * @param spaceId - 空间 ID
   * @param userId - 用户 ID
   * @param timeoutMs - 超时时间（毫秒）
   * @param span - Langfuse span for tracking
   * @returns 最终的 snips 状态
   */
  private async waitForSnipsToComplete(
    snipIds: string[],
    spaceId: string,
    userId: string,
    timeoutMs: number = 120000, // 2 minutes default
    span?: LangfuseSpanClient,
  ): Promise<any[]> {
    const startTime = Date.now();
    const pollInterval = 2000; // 每 2 秒轮询一次

    this.logger.log(`Starting to wait for ${snipIds.length} snips to complete fetching`);

    while (Date.now() - startTime < timeoutMs) {
      try {
        // 查询 snips 的最新状态
        const snips = await this.queryBus.execute(new GetSnipsQuery(snipIds, spaceId, userId));

        // 检查是否所有 snips 都完成了抓取
        const allCompleted = snips.every(
          (snip: any) => !snip.status || snip.status !== SnipStatusEnum.FETCHING,
        );

        if (allCompleted) {
          this.logger.log(`All ${snipIds.length} snips completed fetching`);
          if (span) {
            span.event({
              name: 'snips-fetching-completed',
              input: { snip_ids: snipIds, total_wait_time: Date.now() - startTime },
            });
          }
          // 再发起一次查询，确保 snips 的 blocks 已经准备好
          await sleep(500);
          const snipsWithBlocks = await this.queryBus.execute(
            new GetSnipsQuery(snipIds, spaceId, userId),
          );
          return snipsWithBlocks;
        }

        // 计算还有多少在抓取中
        const fetchingCount = snips.filter(
          (snip: any) => snip.status === SnipStatusEnum.FETCHING,
        ).length;

        this.logger.debug(
          `Still waiting for ${fetchingCount}/${snipIds.length} snips to complete fetching`,
        );

        // 等待一段时间后再次轮询
        await new Promise((resolve) => setTimeout(resolve, pollInterval));
      } catch (error) {
        this.logger.error('Error while polling snips status:', error);
        // 如果查询失败，继续等待
        await new Promise((resolve) => setTimeout(resolve, pollInterval));
      }
    }

    // 超时了，返回最后一次查询的结果
    this.logger.warn(`Timeout waiting for snips to complete after ${timeoutMs}ms`);
    if (span) {
      span.event({
        name: 'snips-fetching-timeout',
        input: { snip_ids: snipIds, timeout_ms: timeoutMs },
      });
    }

    try {
      return await this.queryBus.execute(new GetSnipsQuery(snipIds, spaceId, userId));
    } catch (error) {
      this.logger.error('Failed to get final snips status:', error);
      return [];
    }
  }
}
