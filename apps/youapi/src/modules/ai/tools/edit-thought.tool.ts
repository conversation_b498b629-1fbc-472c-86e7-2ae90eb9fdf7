/**
 * Edit Thought Tool Service - 编辑思想工具服务
 * 创建或编辑思想内容
 *
 * Migrated from:
 * - youapp/src/lib/domain/chat/tool_call/edit_thought.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { LanguageNameMap } from '@repo/common';
import {
  applyJSONToBase64Content,
  contentJSONToBase64,
  DIFF_CHANGE_TYPE,
  diffController,
  diffTransformUtils,
  generateJSON,
  markdownParse,
  SchemaExtension,
} from '@repo/editor-common';
import { LangfuseSpanClient } from 'langfuse-core';
import { Subject } from 'rxjs';
import z from 'zod';
import { YouapiClsService } from '@/common/services/cls.service';
import type { CompletionStreamChunk, StreamChunkUnion } from '@/common/types';
import {
  AdjustLengthDirectionEnum,
  EditCommandTypeEnum,
  ToolNames as ToolNamesEnum,
} from '@/common/types';
import type { ToolCallResult, ToolFunctionParameters } from '@/common/types/tool-call.types';
import { removeImageSuffixes, truncate } from '@/common/utils';
import { EditCommandDto } from '@/modules/chat/dto/chat.dto';
import { ThoughtVersionType } from '@/modules/material-mng/domain/thought-version/models/thought-version.entity';
import { ThoughtDto } from '@/modules/material-mng/dto/thought.dto';
import { ThoughtVersionDto } from '@/modules/material-mng/dto/thought-version/thought-version.dto';
import { CreateThoughtCommand } from '@/modules/material-mng/services/commands/thought/create-thought.command';
import { UpdateThoughtCommand } from '@/modules/material-mng/services/commands/thought/update-thought.command';
import { CreateThoughtVersionCommand } from '@/modules/material-mng/services/commands/thought-version/create-thought-version.command';
import { GetThoughtQuery } from '@/modules/material-mng/services/queries/thought/get-thought.query';
import { GetLatestThoughtVersionQuery } from '@/modules/material-mng/services/queries/thought-version/get-latest-thought-version.query';
import { TextRunnerService } from '../runners/service/text-runner.service';
import { BaseToolService } from './base.service';
import { Tool } from './decorators/tool-register.decorator';

// 定义编辑思想参数的类型
const EditThoughtParameters = z.object({
  instructions: z
    .string()
    .describe(
      'A single sentence instruction describing what you are going to do for the sketched edit. This is used to assist the less intelligent model in applying the edit. Dont repeat what you have said previously in normal messages. And use it to disambiguate uncertainty in the edit.',
    ),
  thought_id: z
    .string()
    .optional()
    .describe('The UUID of the thought to edit. If not provided, a new thought will be created.'),
  title_edit: z
    .string()
    .optional()
    .describe(
      'The new title of the thought. If not provided, the title will not be changed. When creating a new thought, you must always provide the title_edit. Keep the title concise and not too long (recommended under 50 characters).',
    ),
  text_edit: z
    .string()
    .optional()
    .describe(
      "Specify ONLY the precise lines of markdown text that you wish to edit. **NEVER specify the thought's title (H1 heading) in text_edit, use title_edit parameter instead**. **NEVER specify or write out unchanged text**. Instead, represent all unchanged text using the notation - example: `[...]`. **DO NOT** wrap your edits in code blocks using triple backticks (```). Provide the raw text edit directly.",
    ),
  parent_board_group_id: z.string().optional().describe('Optional. Board group ID'),
});

type EditThoughtParameters = z.infer<typeof EditThoughtParameters>;

const EditThoughtDescription = `Use this tool to propose an edit to a thought.
This will be read by a less intelligent model, which will quickly apply the edit. You should make it clear what the edit is, while also minimizing the unchanged text you write.
When writing the edit, you should specify each edit in sequence, with the special notation [...] to represent unchanged text in between edited lines.
For example:
[...]
FIRST_EDIT
[...]
SECOND_EDIT
[...]
THIRD_EDIT
[...]
You should bias towards repeating as few lines of the original thought as possible to convey the change.
But, each edit should contain sufficient context of unchanged lines around the text you are editing to resolve ambiguity.
DO NOT omit spans of pre-existing text without using the [...] notation to indicate its absence.
Make sure it is clear what the edit should be.

If you are creating a new thought, you should specify the following arguments before the others: [title_edit]`;

@Tool({
  name: ToolNamesEnum.EDIT_THOUGHT,
  description: EditThoughtDescription,
  parameters: EditThoughtParameters,
  timeout: 120000, // 2 minutes for thought editing
  maxCalls: 3,
})
@Injectable()
export class EditThoughtService extends BaseToolService {
  private readonly logger = new Logger(EditThoughtService.name);

  constructor(
    protected readonly queryBus: QueryBus,
    protected readonly commandBus: CommandBus,
    protected readonly clsService: YouapiClsService,
    protected readonly textRunnerService: TextRunnerService,
  ) {
    super(queryBus, commandBus);
  }

  async execute(
    parameters: ToolFunctionParameters,
    span: LangfuseSpanClient,
    _subject: Subject<CompletionStreamChunk<StreamChunkUnion>>,
  ): Promise<ToolCallResult> {
    const { parsedParams, userId, chat } = parameters;
    const { thought_id, title_edit, text_edit, instructions, parent_board_group_id } = parsedParams;
    const lastUserMessage = chat.getLastUserMessage();
    const isCommand = !!lastUserMessage?.command;
    const versionTitle = lastUserMessage?.command
      ? this.getCommandTitle(lastUserMessage?.command)
      : truncate((lastUserMessage?.content as string) ?? '', 255);

    const boardId = chat.getBoardId();
    const spaceId = await this.getSpaceId(userId);
    let thought = await this.getThoughtById(thought_id);
    const finalThoughtTitle = title_edit || thought?.title;
    let finalTextEdit = text_edit;
    if (text_edit?.trim().startsWith('# ')) {
      // 使用正则表达式只移除文章开头的 H1 标题
      finalTextEdit = text_edit.replace(/^\s*# .*?\n/, '');
      if (finalThoughtTitle && finalTextEdit.includes(finalThoughtTitle)) {
        // 使用正则表达式移除文章开头的 H1~H6 标题，但仅当标题内容与 finalThoughtTitle 完全一样时
        const headingRegex = new RegExp(`^\\s*(#{1,6})\\s+${finalThoughtTitle}\\s*\\n`, 'i');
        finalTextEdit = finalTextEdit.replace(headingRegex, '');
      }
    }

    // 移除图片的 @large, @medium, @small 后缀，避免 SVG 图片展示问题
    if (finalTextEdit) {
      finalTextEdit = removeImageSuffixes(finalTextEdit);
    }

    // 这种情况一般是只编辑标题
    if (finalTextEdit && finalTextEdit.trim() === '[...]') {
      finalTextEdit = '';
    }

    if (!thought_id) {
      // New Thought
      thought = await this.commandBus.execute(
        new CreateThoughtCommand({
          spaceId,
          creatorId: userId,
          boardId,
          parentBoardGroupId: parent_board_group_id,
          title: title_edit!,
          content: {
            raw: this.markdownToYjsBase64(finalTextEdit || ''),
            plain: finalTextEdit || '',
          },
        }),
      );
      // 记一个版本
      await this.commandBus.execute(
        new CreateThoughtVersionCommand(
          thought.id,
          ThoughtVersionType.AI,
          versionTitle,
          thought.title,
          thought.content.raw,
          thought.content.plain,
          truncate(instructions, 2048),
        ),
      );
      return {
        response: 'The edit_thought tool call was successful. The thought has been created.',
        result: thought as unknown as Record<string, unknown>,
      };
    } else {
      // Edit Thought
      thought = await this.getThoughtById(thought_id);
      const userName = this.clsService.getUserName();
      let newFulltext: string | undefined;
      if (finalTextEdit) {
        const params = {
          fulltext: thought.content.plain || '',
          textEdit: finalTextEdit,
          instructions,
        };
        const prediction = [thought?.content.plain || '', finalTextEdit];
        newFulltext = await this.applyInstantApply(params, prediction, span);
      }
      // 备份修改前的版本
      let latestThoughtVersion = await this.tryGetLatestThoughtVersion(thought_id);
      if (
        !latestThoughtVersion ||
        latestThoughtVersion.content?.plain !== thought.content.plain ||
        latestThoughtVersion.thoughtTitle !== thought.title
      ) {
        latestThoughtVersion = await this.commandBus.execute(
          new CreateThoughtVersionCommand(
            thought.id,
            ThoughtVersionType.BACKUP,
            `${userName} modified`,
            thought.title,
            thought.content.raw,
            thought.content.plain,
            '',
          ),
        );
      }

      const newThought = await this.commandBus.execute(
        new UpdateThoughtCommand({
          thoughtId: thought_id,
          title: title_edit,
          content: newFulltext
            ? {
                raw:
                  chat.isInsideNewBoardWorkflow() || isCommand
                    ? this.markdownToYjsBase64(newFulltext)
                    : await this.applyMarkdownToYjsBase64(thought.content.raw, newFulltext, span),
                plain: newFulltext,
              }
            : undefined,
        }),
      );
      // 记修改后版本
      const currentVersion = await this.commandBus.execute(
        new CreateThoughtVersionCommand(
          newThought.id,
          ThoughtVersionType.AI,
          versionTitle,
          newThought.title,
          newThought.content.raw,
          newThought.content.plain,
          truncate(instructions, 2048),
        ),
      );
      return {
        response: 'The edit_thought tool call was successful. The thought has been updated.',
        result: {
          ...newThought,
          previous_version_id: latestThoughtVersion?.id,
          current_version_id: currentVersion.id,
        },
      };
    }
  }

  private async applyInstantApply(
    params: Record<string, any>,
    prediction: string[],
    trace?: LangfuseSpanClient,
  ): Promise<string> {
    const runner = this.textRunnerService.getInstantApplyRunner(params, prediction);
    runner.setTraceContext(trace);
    const result = await runner.generateOnce();
    return result.text;
  }

  private async getThoughtById(thoughtId?: string): Promise<ThoughtDto | undefined> {
    if (!thoughtId) return undefined;
    const query = new GetThoughtQuery(thoughtId);
    const thought = await this.queryBus.execute(query);
    return thought;
  }

  private async tryGetLatestThoughtVersion(thoughtId: string): Promise<ThoughtVersionDto> {
    const query = new GetLatestThoughtVersionQuery(thoughtId);
    return await this.queryBus.execute(query);
  }

  private getCommandTitle(command: EditCommandDto): string {
    switch (command.type) {
      case EditCommandTypeEnum.ADJUST_LENGTH:
        return command.direction === AdjustLengthDirectionEnum.INCREASE
          ? 'Make longer'
          : 'Make shorter';
      case EditCommandTypeEnum.TRANSLATE:
        return `Translate to ${LanguageNameMap[command.targetLanguage]}`;
      default:
        return '';
    }
  }

  /**
   * 将Markdown内容直接转换为Yjs序列化的base64格式
   */
  private markdownToYjsBase64(markdown: string): string {
    this.logger.debug(`markdownToYjsBase64 newMarkdown: ${markdown}`);
    const html = markdownParse.parse(markdown);
    const json = generateJSON(html, SchemaExtension);
    const base64Content = contentJSONToBase64(json);
    return base64Content;
  }

  /**
   * 将 markdown 应用到一个可能带有 diff 数据的 yjs base64 内容上
   */
  private async applyMarkdownToYjsBase64(
    base64Content: string,
    markdown: string,
    _span: LangfuseSpanClient,
  ): Promise<string> {
    this.logger.debug(
      `applyMarkdownToYjsBase64
oldBase64Content: ${base64Content}
newMarkdown: ${markdown}`,
    );
    const jsonWithoutDiffNode = diffTransformUtils.extractContentFromBase64({
      base64Content,
      extractType: DIFF_CHANGE_TYPE.REMOVED,
    });

    const diffJson = diffController.genDiff({
      oldJSONContent: jsonWithoutDiffNode.toJSON(),
      newMarkdownContent: markdown,
    });
    if (!diffJson) {
      return '';
    }
    return applyJSONToBase64Content(diffJson, base64Content);
  }
}
