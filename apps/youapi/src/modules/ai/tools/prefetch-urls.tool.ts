/**
 * Prefetch URLs Tool Service - URL预抓取工具服务
 * 预先抓取URL的HTML内容并解析元信息
 *
 * 专用于 YouTube board 创建流程的预处理阶段
 */

import { Injectable, Logger } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { LangfuseSpanClient } from 'langfuse-core';
import { Subject } from 'rxjs';
import z from 'zod';
import type { CompletionStreamChunk, StreamChunkUnion } from '@/common/types';
import { ToolNames as ToolNamesEnum } from '@/common/types';
import type { ToolCallResult, ToolFunctionParameters } from '@/common/types/tool-call.types';
import { fetchWithProxy } from '@/common/utils/http';
import { parseWebpageMetaFromHtml } from '@/common/utils/webpage';
import { BaseToolService } from './base.service';
import { Tool } from './decorators/tool-register.decorator';

// 定义预抓取参数类型
const PrefetchUrlsParameters = z.object({
  urls: z.array(z.string().url()).describe('The URLs to prefetch and extract metadata from'),
});

type PrefetchUrlsParameters = z.infer<typeof PrefetchUrlsParameters>;

// 定义返回的元信息类型
interface UrlMetadata {
  url: string;
  title?: string;
  description?: string;
  ogImage?: string;
  ogTitle?: string;
  ogDescription?: string;
  favicon?: string;
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  error?: string;
}

const PrefetchUrlsDescription = `
Prefetch URLs and extract metadata from HTML content including title, description, Open Graph tags, and other meta information
`;

@Tool({
  name: ToolNamesEnum.PREFETCH_URLS,
  description: PrefetchUrlsDescription,
  parameters: PrefetchUrlsParameters,
  timeout: 30000, // 30 seconds
  maxCalls: 1,
})
@Injectable()
export class PrefetchUrlsService extends BaseToolService {
  private readonly logger = new Logger(PrefetchUrlsService.name);

  constructor(
    protected readonly queryBus: QueryBus,
    protected readonly commandBus: CommandBus,
  ) {
    super(queryBus, commandBus);
  }

  /**
   * 从HTML中提取元信息
   * 使用 parseWebpageMetaFromHtml 函数进行解析
   */
  private extractMetadata(html: string, url: string): UrlMetadata {
    try {
      // 使用 parseWebpageMetaFromHtml 函数提取扩展元信息
      const parsedMeta = parseWebpageMetaFromHtml(url, html, { extractExtendedMeta: true });

      const metadata: UrlMetadata = {
        url: parsedMeta.url,
        title: parsedMeta.title,
        description: parsedMeta.description,
        ogTitle: parsedMeta.ogTitle,
        ogDescription: parsedMeta.ogDescription,
        ogImage: parsedMeta.ogImage,
        favicon: parsedMeta.site.favicon_url,
        author: parsedMeta.author,
        publishedTime: parsedMeta.publishedTime,
        modifiedTime: parsedMeta.modifiedTime,
      };

      return metadata;
    } catch (error) {
      this.logger.error(`Error extracting metadata from ${url}:`, error);
      return {
        url,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async execute(
    parameters: ToolFunctionParameters,
    _span: LangfuseSpanClient,
    _subject: Subject<CompletionStreamChunk<StreamChunkUnion>>,
  ): Promise<ToolCallResult> {
    const { parsedParams } = parameters;
    const { urls } = parsedParams as PrefetchUrlsParameters;

    this.logger.log(`Prefetching ${urls.length} URLs`);

    const metadataResults: UrlMetadata[] = [];

    // 并发抓取所有URL
    const fetchPromises = urls.map(async (url) => {
      try {
        this.logger.debug(`Fetching URL: ${url}`);

        const response = await fetchWithProxy(url, {
          timeout: 10000, // 10秒超时
          headers: {
            Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Cache-Control': 'no-cache',
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const html = await response.text();
        const metadata = this.extractMetadata(html, url);

        this.logger.debug(`Successfully extracted metadata from ${url}:`, {
          title: metadata.title,
          hasOgImage: !!metadata.ogImage,
        });

        return metadata;
      } catch (error) {
        this.logger.error(`Failed to fetch ${url}:`, error);
        return {
          url,
          error: error instanceof Error ? error.message : 'Failed to fetch URL',
        };
      }
    });

    const results = await Promise.all(fetchPromises);
    metadataResults.push(...results);

    this.logger.log(
      `Prefetch completed. Successfully fetched ${
        metadataResults.filter((m) => !m.error).length
      }/${urls.length} URLs`,
    );

    return {
      response: `Fetched metadata from ${metadataResults.filter((m) => !m.error).length} of ${urls.length} URLs`,
      result: {
        metadataList: metadataResults,
      },
    };
  }
}
