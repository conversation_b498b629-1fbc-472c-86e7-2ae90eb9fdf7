/**
 * Create Thought From Snip Transcript Tool Service - 从Snip字幕创建思想工具服务
 * 从 snip 的 transcript 数据创建纯文本 thought
 *
 * 专用于 YouTube board 创建流程
 */

import { Injectable, Logger } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import {
  contentJSONToBase64,
  generateJSON,
  markdownParse,
  SchemaExtension,
} from '@repo/editor-common';
import { LangfuseSpanClient } from 'langfuse-core';
import { Subject } from 'rxjs';
import z from 'zod';
import { InvalidArguments } from '@/common/errors';
import type { CompletionStreamChunk, StreamChunkUnion } from '@/common/types';
import {
  CompletionStreamModeEnum,
  type CompletionStreamReplaceChunk,
  ToolNames as ToolNamesEnum,
} from '@/common/types';
import type { ToolCallResult, ToolFunctionParameters } from '@/common/types/tool-call.types';
import { SafeParse, sleep } from '@/common/utils';
import { ReaderHTMLContent } from '@/modules/material-mng/domain/snip/value-objects/content.vo';
import { WebpageMeta } from '@/modules/material-mng/domain/snip/value-objects/webpage-meta.vo';
import { ThoughtVersionType } from '@/modules/material-mng/domain/thought-version/models/thought-version.entity';
import { BoardGroupDto } from '@/modules/material-mng/dto/board-group.dto';
import { EntityType } from '@/modules/material-mng/dto/move/move-entity-to-unsorted.dto';
import { ImageDto } from '@/modules/material-mng/dto/snip/image.dto';
import { PdfDto } from '@/modules/material-mng/dto/snip/pdf.dto';
import { UnknownWebpageDto } from '@/modules/material-mng/dto/snip/unknown-webpage.dto';
import { VoiceDto } from '@/modules/material-mng/dto/snip/voice.dto';
import { CreateBoardGroupCommand } from '@/modules/material-mng/services/commands/board-group/create-board-group.command';
import { MoveItemCommand } from '@/modules/material-mng/services/commands/move/move-item.command';
import { CreateArticleCommand } from '@/modules/material-mng/services/commands/snip/create-article.command';
import { TryCreateSnipByUrlCommand } from '@/modules/material-mng/services/commands/snip/try-create-snip-by-url.command';
import { CreateThoughtCommand } from '@/modules/material-mng/services/commands/thought/create-thought.command';
import { CreateThoughtVersionCommand } from '@/modules/material-mng/services/commands/thought-version/create-thought-version.command';
import { GetSnipQuery } from '@/modules/material-mng/services/queries/snip/get-snip.query';
import { BaseToolService } from './base.service';
import { Tool } from './decorators/tool-register.decorator';

// 定义从 snip transcript 创建 thought 参数的类型
const YoutubeFactoryParameters = z.object({});

type YoutubeFactoryParameters = z.infer<typeof YoutubeFactoryParameters>;

// Board Group 名称类型定义
const BOARD_GROUP_NAMES = {
  OVERVIEW: 'Overview',
  TRANSCRIPT: 'Transcript',
  TAKEAWAYS: 'Takeaways',
  LEARNING: 'Learning',
  RELATED: 'Related',
} as const;

type BoardGroupName = (typeof BOARD_GROUP_NAMES)[keyof typeof BOARD_GROUP_NAMES];

const YoutubeFactoryDescription = `
Process a YouTube video
`;

const RELATED_VIDEO_COUNT = 4;

@Tool({
  name: ToolNamesEnum.YOUTUBE_FACTORY,
  description: YoutubeFactoryDescription,
  parameters: YoutubeFactoryParameters,
  timeout: 60000, // 1 minute
  maxCalls: 1,
})
@Injectable()
export class YoutubeFactoryService extends BaseToolService {
  private readonly logger = new Logger(YoutubeFactoryService.name);

  constructor(
    protected readonly queryBus: QueryBus,
    protected readonly commandBus: CommandBus,
  ) {
    super(queryBus, commandBus);
  }

  /**
   * 将Markdown内容直接转换为Yjs序列化的base64格式
   */
  private markdownToYjsBase64(markdown: string): string {
    this.logger.debug(`markdownToYjsBase64 newMarkdown: ${markdown}`);
    const html = markdownParse.parse(markdown);
    const json = generateJSON(html, SchemaExtension);
    const base64Content = contentJSONToBase64(json);
    return base64Content;
  }

  /**
   * 通用方法：根据 group 名称获取对应的 ID
   * @param groupName - Board Group 的名称
   * @param createdGroups - 已创建的 Board Group 数组
   * @returns 对应的 Board Group ID，如果未找到则返回 undefined
   */
  private findGroupIdByName(
    groupName: BoardGroupName,
    createdGroups: BoardGroupDto[],
  ): string | undefined {
    const group = createdGroups.find((g) => g.name === groupName);
    return group?.id;
  }

  /**
   * 从转录文本中提取金句
   * TODO 换成 AI 来找
   * @param transcriptText - 转录文本，格式为时间戳:内容
   * @returns 提取的3句金句数组
   */
  private extractGoldenQuotes(transcriptText: string): string[] {
    try {
      // 预处理：移除时间戳，提取纯文本
      const cleanText = transcriptText
        .split('\n')
        .map((line) => {
          // 移除时间戳格式 "0:04: "
          return line.replace(/^\d+:\d+:\s*/, '').trim();
        })
        .filter((line) => line.length > 0)
        .join(' ');

      // 按句号、问号、感叹号分割句子
      const sentences = cleanText
        .split(/[.!?]+/)
        .map((sentence) => sentence.trim())
        .filter((sentence) => {
          // 过滤条件：
          // 1. 句子长度至少包含3个单词
          // 2. 不包含常见的填充词或不完整句子
          const wordCount = sentence.split(/\s+/).filter((word) => word.length > 0).length;
          const isValidSentence =
            wordCount >= 3 &&
            !sentence.match(/^(um|uh|er|ah|well|so|and|but|the|a|an|in|on|at|to|for)$/i) &&
            sentence.length > 10; // 至少10个字符
          return isValidSentence;
        });

      // 如果没有找到足够的句子，使用段落分割
      if (sentences.length < 3) {
        const paragraphs = cleanText
          .split(/[,;:]/)
          .map((part) => part.trim())
          .filter((part) => {
            const wordCount = part.split(/\s+/).filter((word) => word.length > 0).length;
            return wordCount >= 3 && part.length > 15;
          });

        sentences.push(...paragraphs);
      }

      // 随机选择3句
      const selectedQuotes = [];
      const availableSentences = [...sentences];

      for (let i = 0; i < Math.min(3, availableSentences.length); i++) {
        const randomIndex = Math.floor(Math.random() * availableSentences.length);
        const selectedSentence = availableSentences.splice(randomIndex, 1)[0];

        // 确保句子以适当的标点结尾
        let finalQuote = selectedSentence;
        if (!finalQuote.match(/[.!?]$/)) {
          finalQuote += '.';
        }

        selectedQuotes.push(finalQuote);
      }

      // 如果还是没有足够的句子，用默认文本填充
      while (selectedQuotes.length < 3) {
        selectedQuotes.push(`Key insight from the video content.`);
      }

      this.logger.log(`Extracted ${selectedQuotes.length} golden quotes from transcript`);
      return selectedQuotes;
    } catch (error) {
      this.logger.error('Failed to extract golden quotes from transcript:', error);
      // 返回默认的金句
      return [
        'Key insight from the video content.',
        'Important takeaway from the discussion.',
        'Notable quote from the presentation.',
      ];
    }
  }

  async execute(
    parameters: ToolFunctionParameters,
    _span: LangfuseSpanClient,
    subject: Subject<CompletionStreamChunk<StreamChunkUnion>>,
  ): Promise<ToolCallResult> {
    const { parsedParams, userId, chat, completionBlock } = parameters;

    // 获取必要的信息
    const boardId = chat.getBoardId();
    const spaceId = await this.getSpaceId(userId);

    if (!boardId) {
      throw new InvalidArguments('Board ID is required but not found in chat context');
    }

    // 初始化进度状态
    subject.next({
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'CompletionBlock',
      targetId: completionBlock.id,
      data: {
        status: 'starting',
        step: 'Initializing YouTube video processing',
        progress: 0,
        total: 6, // 总共6个步骤：获取snip、创建groups、创建thought、创建quiz、创建相关视频、创建金句卡片
      },
      path: 'tool_result.progress',
    } as CompletionStreamReplaceChunk<StreamChunkUnion>);

    /**
     * 第1步：获取 Snip
     */
    subject.next({
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'CompletionBlock',
      targetId: completionBlock.id,
      data: {
        status: 'processing',
        step: 'Fetching video transcript',
        progress: 1,
        total: 6,
      },
      path: 'tool_result.progress',
    } as CompletionStreamReplaceChunk<StreamChunkUnion>);

    await sleep(500);

    // 找到目标 snip 的 transcript
    const targetSnip = chat
      .getLastAssistantMessage()
      // @ts-expect-error
      .blocks.find((b) => b.toolName === ToolNamesEnum.CREATE_SNIP_BY_URL)?.toolResult
      ?.snipsResults?.[0]?.snip;

    // 这里总是拿不到最新的，所以查一下最新的
    const snip = await this.queryBus.execute(new GetSnipQuery(targetSnip.id, spaceId, userId));

    // 通知前端获取到了 snip 信息
    subject.next({
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'CompletionBlock',
      targetId: completionBlock.id,
      data: {
        snip_id: snip.id,
        title: snip.title,
        has_transcript: !!snip?.transcript?.contents?.[0]?.plain,
      },
      path: 'tool_result.snip_info',
    } as CompletionStreamReplaceChunk<StreamChunkUnion>);

    /**
     * 第2步：创建 Board Groups
     */
    subject.next({
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'CompletionBlock',
      targetId: completionBlock.id,
      data: {
        status: 'processing',
        step: 'Creating board groups',
        progress: 2,
        total: 6,
      },
      path: 'tool_result.progress',
    } as CompletionStreamReplaceChunk<StreamChunkUnion>);

    // 定义要创建的 board groups
    const groupNames = Object.values(BOARD_GROUP_NAMES).reverse();
    const createdGroups: BoardGroupDto[] = [];

    // 批量并发创建 board groups
    const createGroupPromises = groupNames.map(async (groupName) => {
      try {
        const createGroupCommand = new CreateBoardGroupCommand(
          userId,
          spaceId,
          boardId,
          groupName,
          {}, // 默认图标
          undefined, // 默认类型
        );

        const createdGroup = await this.commandBus.execute(createGroupCommand);
        this.logger.log(`Successfully created board group: ${groupName} (${createdGroup.id})`);
        return createdGroup;
      } catch (error) {
        this.logger.error(`Failed to create board group ${groupName}:`, error);
        return null; // 返回 null 表示创建失败
      }
    });

    const groupResults = await Promise.all(createGroupPromises);
    // 过滤掉创建失败的 groups
    createdGroups.push(...groupResults.filter((group) => group !== null));

    // 通知前端 board groups 创建完成，触发板面刷新
    subject.next({
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'CompletionBlock',
      targetId: completionBlock.id,
      data: {
        groups: createdGroups,
        created_count: createdGroups.length,
        total_count: groupNames.length,
        refresh_board: true, // 提示前端需要刷新 board
      },
      path: 'tool_result.board_groups',
    } as CompletionStreamReplaceChunk<StreamChunkUnion>);

    console.log('需要揉碎的 Snip', snip);

    // 将目标 targetSnip 添加到 Overview Group
    const overviewGroupId = this.findGroupIdByName(BOARD_GROUP_NAMES.OVERVIEW, createdGroups);
    if (overviewGroupId) {
      const command = new MoveItemCommand(
        EntityType.SNIP,
        snip.id,
        boardId,
        userId,
        undefined, // rankAfter
        overviewGroupId, // 移动到指定分组
      );
      await this.commandBus.execute(command);
    }

    /**
     * 第3步：创建 transcript thought
     */

    const transcriptText = snip?.transcript?.contents?.[0]?.plain;
    const finalTitle = snip?.title;

    // TODO 如果 transcriptText 为空，要特殊处理

    this.logger.log(
      `Creating thought from transcript. Title: "${finalTitle}", Content length: ${transcriptText?.length}`,
    );

    // 通知前端正在创建 thought
    subject.next({
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'CompletionBlock',
      targetId: completionBlock.id,
      data: {
        status: 'processing',
        step: 'Creating thought from transcript',
        progress: 3,
        total: 6,
      },
      path: 'tool_result.progress',
    } as CompletionStreamReplaceChunk<StreamChunkUnion>);

    // 创建 thought
    const thought = await this.commandBus.execute(
      new CreateThoughtCommand({
        spaceId,
        creatorId: userId,
        boardId,
        title: finalTitle,
        parentBoardGroupId: this.findGroupIdByName(BOARD_GROUP_NAMES.TRANSCRIPT, createdGroups),
        content: {
          raw: this.markdownToYjsBase64(transcriptText),
          plain: transcriptText,
        },
      }),
    );

    // 创建版本记录
    await this.commandBus.execute(
      new CreateThoughtVersionCommand(
        thought.id,
        ThoughtVersionType.AI,
        'Created from YouTube video transcript',
        thought.title,
        thought.content.raw,
        thought.content.plain,
        'Automatically extracted transcript text from YouTube video snip',
      ),
    );

    this.logger.log(`Successfully created thought ${thought.id} from transcript`);

    // 通知前端 thought 创建成功，传递完整的 thought 数据
    subject.next({
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'CompletionBlock',
      targetId: completionBlock.id,
      data: {
        thought_id: thought.id,
        title: thought.title,
        content_length: transcriptText.length,
        board_id: boardId,
        thought: thought, // 传递完整的 thought 实体
      },
      path: 'tool_result.thought_result',
    } as CompletionStreamReplaceChunk<StreamChunkUnion>);

    /**
     * 第4步：创建 quiz snip
     */

    // 通知前端正在创建 quiz
    subject.next({
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'CompletionBlock',
      targetId: completionBlock.id,
      data: {
        status: 'processing',
        step: 'Creating quiz from video content',
        progress: 4,
        total: 6,
      },
      path: 'tool_result.progress',
    } as CompletionStreamReplaceChunk<StreamChunkUnion>);

    // 创建 quiz snip
    const webpage = new WebpageMeta(
      'quiz://youmind',
      'quiz://youmind',
      '测验',
      'AI Generated Quiz',
      {
        name: 'YouMind_Quiz_V1',
        host: 'youmind.quiz',
        faviconUrl: '',
      },
    );

    // TODO 应该让模型来生成

    const content = new ReaderHTMLContent(
      '# 大多数千禧一代认为致富是他们最重要的生活目标之一。\n### 1\n\n# 只有独居的人才会感到孤独。\n### 0\n\n# 拥有大量朋友是维持健康幸福生活的关键。\n### 0\n\n# 良好的亲密关系有助于保护大脑功能。\n### 1\n\n# 哈佛成人发展研究得出的最明确信息是什么？\n## 财富和名望\n## 努力工作和成就\n## 良好的亲密关系\n## 个人职业成功\n### 3\n\n# 哈佛成人发展研究持续了多长时间？\n## 超过十年\n## 约二十五年\n## 超过七十年\n## 仅限于研究人员的寿命\n### 3\n\n# 根据研究，与他人隔离的人会怎样？\n## 更快乐\n## 身体更健康\n## 大脑功能下降更快\n## 寿命更长\n### 3\n\n# 研究表明，充满冲突但缺乏感情的婚姻对健康有何影响？\n## 有益于心理健康\n## 对健康影响不大\n## 对健康非常不利，可能比离婚更糟\n## 促进个人成长\n### 3\n\n# 在研究中，什么因素在50岁时就能预测一个人80岁时的健康状况？\n## 胆固醇水平\n## 职业成就\n## 对人际关系的满意度\n## 经济财富\n### 3\n\n# 为什么"良好亲密关系有益健康和幸福"的智慧很难被人们采纳？\n## 因为人们更喜欢快速见效的方法\n## 因为关系总是平稳且容易维护\n## 因为关系不重要\n## 因为人们普遍不相信这项研究\n### 1',
      transcriptText,
    );

    const quizSnip = await this.commandBus.execute(
      new CreateArticleCommand(
        spaceId,
        userId,
        `Quiz: ${finalTitle}`,
        webpage,
        content,
        undefined, // authors
        undefined, // heroImageUrl
        undefined, // publishedAt
        undefined, // playUrl
        undefined, // extra
        undefined, // overviewContents
        undefined, // transcriptContents
        boardId,
        this.findGroupIdByName(BOARD_GROUP_NAMES.LEARNING, createdGroups), // parentBoardGroupId
        undefined, // chatId
      ),
    );

    // 通知前端 quiz 创建成功，传递完整的 snip 数据
    subject.next({
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'CompletionBlock',
      targetId: completionBlock.id,
      data: {
        quiz_id: quizSnip.id,
        title: `Quiz: ${finalTitle}`,
        board_id: boardId,
        snip: quizSnip, // 传递完整的 quiz snip 实体
      },
      path: 'tool_result.quiz_result',
    } as CompletionStreamReplaceChunk<StreamChunkUnion>);

    this.logger.log(`Successfully created quiz snip ${quizSnip.id} from transcript`);

    /**
     * 第5步：创建 related snip
     */
    const relatedVideoIds = SafeParse<{ relatedVideos?: string[] }>(snip.extra)?.relatedVideos;
    const slicedRelatedVideoIds = relatedVideoIds?.slice(0, RELATED_VIDEO_COUNT);

    // 初始化相关视频结果数组
    interface RelatedSnipResult {
      video_id: string;
      snip_id?: string;
      title?: string;
      url: string;
      success: boolean;
      snip?: ImageDto | VoiceDto | PdfDto | UnknownWebpageDto;
      error?: string;
    }
    const relatedSnips: RelatedSnipResult[] = [];

    if (slicedRelatedVideoIds && slicedRelatedVideoIds.length > 0) {
      // 通知前端正在创建相关视频
      subject.next({
        mode: CompletionStreamModeEnum.REPLACE,
        targetType: 'CompletionBlock',
        targetId: completionBlock.id,
        data: {
          status: 'processing',
          step: 'Creating related video snips',
          progress: 5,
          total: 6,
          related_videos_count: slicedRelatedVideoIds.length,
        },
        path: 'tool_result.progress',
      } as CompletionStreamReplaceChunk<StreamChunkUnion>);

      // 初始化相关视频结果数组
      subject.next({
        mode: CompletionStreamModeEnum.REPLACE,
        targetType: 'CompletionBlock',
        targetId: completionBlock.id,
        data: [],
        path: 'tool_result.related_snips',
      } as CompletionStreamReplaceChunk<StreamChunkUnion>);

      // 通知前端开始并发处理相关视频
      subject.next({
        mode: CompletionStreamModeEnum.REPLACE,
        targetType: 'CompletionBlock',
        targetId: completionBlock.id,
        data: {
          processing_status: 'concurrent_processing',
          total_videos: slicedRelatedVideoIds.length,
        },
        path: 'tool_result.related_progress',
      } as CompletionStreamReplaceChunk<StreamChunkUnion>);

      // 并发处理所有相关视频
      const relatedVideoPromises = slicedRelatedVideoIds.map(async (videoId, index) => {
        try {
          const command = new TryCreateSnipByUrlCommand(
            spaceId,
            userId,
            `https://www.youtube.com/watch?v=${videoId}`,
            boardId,
            this.findGroupIdByName(BOARD_GROUP_NAMES.RELATED, createdGroups), // parentBoardGroupId
          );

          const result = await this.commandBus.execute(command);
          console.log('成功创建 related snip', result);

          if (result) {
            const relatedSnip = {
              video_id: videoId,
              snip_id: result.id,
              title: result.title,
              url: `https://www.youtube.com/watch?v=${videoId}`,
              success: true,
              snip: result, // 确保内存数组也包含完整数据
            };

            // 实时更新相关视频结果，传递完整的 snip 数据
            subject.next({
              mode: CompletionStreamModeEnum.REPLACE,
              targetType: 'CompletionBlock',
              targetId: completionBlock.id,
              data: {
                video_id: videoId,
                snip_id: result.id,
                title: result.title,
                url: `https://www.youtube.com/watch?v=${videoId}`,
                success: true,
                snip: result, // 传递完整的 snip 实体
              },
              path: `tool_result.related_snips[${index}]`,
            } as CompletionStreamReplaceChunk<StreamChunkUnion>);

            return relatedSnip;
          }
          return null;
        } catch (error) {
          this.logger.error(`Failed to create related snip for video ${videoId}:`, error);

          // 记录失败的相关视频
          subject.next({
            mode: CompletionStreamModeEnum.REPLACE,
            targetType: 'CompletionBlock',
            targetId: completionBlock.id,
            data: {
              video_id: videoId,
              url: `https://www.youtube.com/watch?v=${videoId}`,
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error',
            },
            path: `tool_result.related_snips[${index}]`,
          } as CompletionStreamReplaceChunk<StreamChunkUnion>);

          return {
            video_id: videoId,
            url: `https://www.youtube.com/watch?v=${videoId}`,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
          };
        }
      });

      // 等待所有相关视频处理完成
      const relatedResults = await Promise.all(relatedVideoPromises);

      // 过滤出成功的结果并添加到 relatedSnips
      relatedSnips.push(...relatedResults.filter((result) => result?.success));
    }

    /**
     * 第6步：创建金句卡片
     */

    // 通知前端正在创建金句卡片
    subject.next({
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'CompletionBlock',
      targetId: completionBlock.id,
      data: {
        status: 'processing',
        step: 'Creating golden quote cards',
        progress: 6,
        total: 6,
      },
      path: 'tool_result.progress',
    } as CompletionStreamReplaceChunk<StreamChunkUnion>);

    // 从转录文本中提取金句
    const extractedQuotes = this.extractGoldenQuotes(transcriptText);

    // 创建三个金句卡片 thoughts
    const goldenQuoteThoughts = [];

    for (const [index, quote] of extractedQuotes.entries()) {
      try {
        const quoteName = `Golden Quote ${index + 1}`;
        const goldenQuoteThought = await this.commandBus.execute(
          new CreateThoughtCommand({
            spaceId,
            creatorId: userId,
            boardId,
            title: quoteName,
            parentBoardGroupId: this.findGroupIdByName(BOARD_GROUP_NAMES.TAKEAWAYS, createdGroups),
            content: {
              raw: this.markdownToYjsBase64(quote), // 使用提取的金句
              plain: quote, // 使用提取的金句
            },
          }),
        );

        // 创建版本记录
        await this.commandBus.execute(
          new CreateThoughtVersionCommand(
            goldenQuoteThought.id,
            ThoughtVersionType.AI,
            'Created golden quote extracted from video transcript',
            goldenQuoteThought.title,
            goldenQuoteThought.content.raw,
            goldenQuoteThought.content.plain,
            'Golden quote automatically extracted from YouTube video transcript',
          ),
        );

        goldenQuoteThoughts.push(goldenQuoteThought);

        this.logger.log(
          `Successfully created golden quote thought ${goldenQuoteThought.id}: "${quote.substring(0, 50)}..."`,
        );
      } catch (error) {
        this.logger.error(`Failed to create golden quote thought ${index + 1}:`, error);
        // 继续创建其他金句卡片，不因单个失败而终止
      }
    }

    // 通知前端金句卡片创建成功
    subject.next({
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'CompletionBlock',
      targetId: completionBlock.id,
      data: {
        golden_quote_thoughts: goldenQuoteThoughts,
        created_count: goldenQuoteThoughts.length,
        total_count: extractedQuotes.length,
        board_id: boardId,
      },
      path: 'tool_result.golden_quotes_result',
    } as CompletionStreamReplaceChunk<StreamChunkUnion>);

    this.logger.log(`Successfully created ${goldenQuoteThoughts.length} golden quote thoughts`);

    // 发送完成通知
    subject.next({
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'CompletionBlock',
      targetId: completionBlock.id,
      data: {
        status: 'completed',
        step: 'All processing completed',
        progress: 6,
        total: 6,
      },
      path: 'tool_result.progress',
    } as CompletionStreamReplaceChunk<StreamChunkUnion>);

    // 包装一下 group 信息
    const boardGroups = createdGroups.map((group) => ({
      id: group.id,
      name: group.name,
    }));

    // 发送总结信息
    const summaryMessage = `Successfully processed YouTube video "${finalTitle}". Created 1 thought (${transcriptText.length} characters), 1 quiz, ${relatedSnips.length} related video snips, and ${goldenQuoteThoughts.length} golden quote cards.
    Board Groups Data: ${JSON.stringify(boardGroups)}
    `;

    subject.next({
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'CompletionBlock',
      targetId: completionBlock.id,
      data: summaryMessage,
      path: 'tool_result.summary_message',
    } as CompletionStreamReplaceChunk<StreamChunkUnion>);

    return {
      response: summaryMessage,
      result: {
        // 保留所有之前的通知数据
        progress: {
          status: 'completed',
          step: 'All processing completed',
          progress: 6,
          total: 6,
        },
        board_groups: {
          groups: createdGroups,
          created_count: createdGroups.length,
          total_count: groupNames.length,
          refresh_board: true,
        },
        snip_info: {
          snip_id: snip.id,
          title: snip.title,
          has_transcript: !!snip?.transcript?.contents?.[0]?.plain,
        },
        thought_result: {
          thought_id: thought.id,
          title: thought.title,
          content_length: transcriptText.length,
          board_id: boardId,
          thought: thought,
        },
        quiz_result: {
          quiz_id: quizSnip.id,
          title: `Quiz: ${finalTitle}`,
          board_id: boardId,
          snip: quizSnip,
        },
        related_snips: relatedSnips,
        related_progress: {
          processing_index: slicedRelatedVideoIds?.length || 0,
          processing_video_id: null,
          total_videos: slicedRelatedVideoIds?.length || 0,
        },
        golden_quotes_result: {
          golden_quote_thoughts: goldenQuoteThoughts,
          created_count: goldenQuoteThoughts.length,
          total_count: extractedQuotes.length,
          board_id: boardId,
        },
        summary_message: summaryMessage,
        // 为了兼容性，保留原有字段
        thought_id: thought.id,
        title: thought.title,
        content_length: transcriptText.length,
        quiz_id: quizSnip.id,
        transcript_text: transcriptText,
      },
    };
  }
}
