import { Line, LineSvgProps } from '@nivo/line';
import { formatThousands } from '@repo/common';
import { SimpleLoading } from '@repo/ui/components/custom/simple-loading';
import { AddonCreditsDialog } from '@repo/ui/components/custom/subscription/addon-credits-dialog';
// import { HoverCard, HoverCardContent, HoverCardTrigger } from '@repo/ui/components/ui/hover-card';
import { useAsyncEffect } from 'ahooks';
import { format } from 'date-fns';
import { ChevronUp } from 'lucide-react';
import { useEffect, useLayoutEffect, useRef, useState } from 'react';
import { useCreditAccount } from '@/hooks/useSubscription';
import { useTranslation } from '@/hooks/useTranslation';
import { apiClient, callAPI } from '@/utils/callHTTP';
import { cn } from '@/utils/utils';
// import { CoinIcon } from './icon/coin';
import { Progress } from './ui/progress';

interface LineData {
  id: string;
  color: string;
  data: {
    x: Date;
    y: number;
  }[];
}

const categories: {
  id: string;
  legend: string;
  color: string;
}[] = [
  {
    id: 'chat',
    legend: 'Chat',
    color: '#C6BC61',
  },
  {
    id: 'writing',
    legend: 'Writing',
    color: '#A088AD',
  },
  {
    id: 'search',
    legend: 'Search',
    color: '#6E9AD2',
  },
  {
    id: 'image',
    legend: 'Image',
    color: '#8DAF87',
  },
  {
    id: 'audio',
    legend: 'Audio',
    color: '#C89469',
  },
  {
    id: 'parsing',
    legend: 'Parsing',
    color: '#708090',
  },
];

const X_SCALE_WIDTH = 80;

export const CreditsUsage: React.FC<CreditsUsageProps> = ({ credits, usage, daysLeft }) => {
  const { creditUsagePanelOpen, setCreditUsagePanelOpen } = useCreditAccount();
  const chartRef = useRef<HTMLDivElement>(null);
  const [chartWidth, setChartWidth] = useState(0);
  const [addonCreditsDialogOpen, setAddonCreditsDialogOpen] = useState(false);

  const scrollRef = useRef<HTMLDivElement>(null);

  const currentDate = new Date();
  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();
  const currentDay = currentDate.getDate();
  const currentMonthStart = new Date(currentYear, currentMonth, 1);
  const currentMonthEnd = new Date(currentYear, currentMonth + 1, 0);
  const daysInCurrentMonth = new Date(currentYear, currentMonth + 1, 0).getDate();

  const { t } = useTranslation('Settings.Account');

  const [loading, setLoading] = useState(false);
  const [lines, setLines] = useState<LineData[]>([]);
  const [filteredLines, setFilteredLines] = useState<LineData[]>([]);
  const [hiddenIds, setHiddenIds] = useState<string[]>([]);

  const legendsData = categories.map((category) => ({
    id: category.id,
    label: category.legend,
    color: category.color,
    total:
      lines.find((line) => line.id === category.id)?.data?.reduce((acc, curr) => acc + curr.y, 0) ??
      0,
  }));

  useLayoutEffect(() => {
    setTimeout(() => {
      if (chartRef.current) {
        setChartWidth(chartRef.current.clientWidth ?? 0);
      }
    }, 100);

    return () => {
      setCreditUsagePanelOpen(false);
    };
  }, []);

  useEffect(() => {
    if (scrollRef.current && lines.length > 0 && creditUsagePanelOpen) {
      const scrollPosition = Math.max(0, (currentDay - 8) * X_SCALE_WIDTH);
      scrollRef.current.scrollLeft = scrollPosition;
    }
  }, [lines, currentDay, creditUsagePanelOpen]);

  useEffect(() => {
    setFilteredLines(lines.filter((line) => !hiddenIds.includes(line.id)));
  }, [lines, hiddenIds]);

  useAsyncEffect(async () => {
    setLoading(true);
    const { data } = await callAPI(
      apiClient.creditApi.listCreditsConsumeTransactionsInCurrentPeriod(),
    );
    if (data) {
      // 按照 categories 初始化 lines
      const lines: LineData[] = categories.map((category) => ({
        id: category.id,
        color: category.color,
        data: [],
      }));

      data.transactions.forEach((transaction) => {
        const transactionDate = new Date(transaction.created_at);
        const transactionMonth = transactionDate.getMonth();
        const transactionYear = transactionDate.getFullYear();

        // 只保留当前月份的数据
        if (transactionMonth === currentMonth && transactionYear === currentYear) {
          const category = (transaction.metadata as any).category;
          if (category) {
            const line = lines.find((line) => line.id === category);
            if (line) {
              line.data.push({
                x: transaction.created_at,
                y: transaction.amount,
              });
            }
          }
        }
      });

      const groupedLines: LineData[] = [];
      lines.forEach((line) => {
        const groupedData: { x: Date; y: number }[] = [];
        for (let i = 0; i < currentDay; i++) {
          groupedData.push({
            x: new Date(currentYear, currentMonth, i + 1),
            y: 0,
          });
        }

        line.data.forEach((data) => {
          data.x = new Date(data.x.getFullYear(), data.x.getMonth(), data.x.getDate());
          const existingData = groupedData.find(
            (d) => format(d.x, 'yyyy MMM dd') === format(data.x, 'yyyy MMM dd'),
          );
          if (existingData) {
            existingData.y += data.y;
          }
        });
        groupedLines.push({
          ...line,
          data: groupedData,
        });
      });

      setLines(groupedLines);
    }
    setLoading(false);
  }, []);

  const props: LineSvgProps<LineData> = {
    width: chartWidth,
    height: 400,
    theme: {
      axis: {
        ticks: {
          text: {
            fill: 'rgba(0, 0, 0, 0.4)',
          },
        },
      },
    },
    colors: { datum: 'color' },
    animate: true,
    data: filteredLines,
    enableSlices: 'x',
    enableTouchCrosshair: true,
    enableGridX: false,
    enableArea: true,
    areaOpacity: 0.07,
    pointSize: 0,
    margin: {
      bottom: 32,
      left: 40,
      right: 20,
      top: 12,
    },
    xScale: {
      type: 'time',
      precision: 'day',
      useUTC: false,
      min: currentMonthStart,
      max: currentMonthEnd,
    },
    yScale: { type: 'linear', min: 0, max: 'auto' },
    axisLeft: {
      tickValues: 5,
    },
    axisBottom: {
      format: '%b %d',
      tickValues: 'every 1 day',
    },
    sliceTooltip: ({ slice }) => {
      return (
        <div
          className="p-3 rounded-lg"
          style={{
            borderRadius: 12,
            border: '1px solid #F7F7F7',
            background: 'rgba(255, 255, 255, 0.24)',
            boxShadow: '0 2px 8px 0 rgba(2, 4, 26, 0.06)',
            backdropFilter: 'blur(10px)',
          }}
        >
          <p className="body-strong mb-2">{format(slice.points[0].data.x, 'MMM dd')}</p>
          <ul className="flex flex-col gap-y-1">
            {categories.map((category) => {
              const point = slice.points.find((point) => point.seriesId === category.id);
              if (!point) return null;

              return (
                <li
                  className="text-secondary-fg text-xs flex items-center justify-between gap-x-4"
                  key={point.id}
                >
                  <div className="flex items-center gap-x-1">
                    <div
                      className="w-3 h-3 rounded-[2px] border border-divider"
                      style={{
                        backgroundColor: point.seriesColor,
                      }}
                    ></div>
                    <div>
                      {categories.find((category) => category.id === point.seriesId)?.legend}
                    </div>
                  </div>
                  <div className="text-right">{formatThousands(point.data.y)}</div>
                </li>
              );
            })}
          </ul>
        </div>
      );
    },
  };

  const handleToggleLegend = (id: string) => {
    setHiddenIds((prev) => {
      if (prev.includes(id)) {
        return prev.filter((i) => i !== id);
      }
      return [...prev, id];
    });
  };

  return (
    <div className="px-4 mb-2" ref={chartRef}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {/* <CoinIcon size={16} className="text-secondary-fg" /> */}
          <span className="body-strong">Credits usage</span>
          <span className="text-caption-fg footnote">
            {t('refreshInDays', { count: daysLeft })}
          </span>
        </div>
        <div
          className="flex items-center text-caption-fg footnote cursor-pointer"
          onClick={() => setCreditUsagePanelOpen(!creditUsagePanelOpen)}
        >
          <span>{formatThousands(usage - Math.max(0, credits ?? 0))}</span>/
          <span>{formatThousands(usage)}</span>
          {/* <HoverCard openDelay={0}>
            <HoverCardTrigger asChild>
              <CircleQuestionMark size={12} className="text-caption-fg mx-1" />
            </HoverCardTrigger>
            <HoverCardContent
              className="w-[292px] p-5 rounded-xl border border-divider space-y-2 cursor-default"
              onClick={(e) => e.stopPropagation()}
            >
              <p className="body-strong">Your credit limit include:</p>
              <p className="body">
                Monthly credits are used first. Once they’re used up, permanent bonus credits are
                used.
              </p>
              <ul className="body text-secondary-fg list-disc list-inside">
                <li>
                  {formatThousands(usage)} <span className="body-strong">monthly</span> credits from
                  plan (reset every month)
                </li>
                <li>
                  0 <span className="body-strong">permanent</span> add-on credits (remain until
                  used)
                </li>
              </ul>
              <p
                className="body text-function-link flex items-center cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  setAddonCreditsDialogOpen(true);
                }}
              >
                <CoinIcon size={16} className="mr-1" />
                View add-on credit history
              </p>
            </HoverCardContent>
          </HoverCard> */}
          <ChevronUp size="16" className={cn(!creditUsagePanelOpen ? 'rotate-180' : '')} />
        </div>
      </div>
      <Progress
        className="h-1 mt-2 bg-[#F2F2F2] mb-6"
        indicatorClassName="bg-gradient-to-r from-[#A2A0B0] to-[#615B7F]"
        value={(1 - credits / usage) * 100}
      />
      {creditUsagePanelOpen && (
        <div className="bg-snip-card rounded-[20px] p-4 flex flex-col gap-y-2">
          <div className="flex items-center gap-x-5 ml-3">
            {legendsData.map((legend) => (
              <div
                key={legend.id}
                className={cn(
                  'flex items-center gap-x-1 footnote',
                  !loading && 'cursor-pointer',
                  hiddenIds.includes(legend.id) && 'opacity-50',
                )}
                onClick={() => !loading && handleToggleLegend(legend.id)}
              >
                <div
                  className="w-3 h-3 rounded-[2px] border border-divider"
                  style={{ backgroundColor: legend.color }}
                ></div>
                <span>
                  {legend.label}
                  <span className="text-caption-fg ml-1">{formatThousands(legend.total)}</span>
                </span>
              </div>
            ))}
          </div>
          {loading ? (
            <SimpleLoading />
          ) : (
            <div className="flex w-full">
              <Line {...props} layers={['axes']} axisBottom={null} width={50} />
              <div
                className="flex-1 overflow-x-auto overflow-y-hidden"
                style={{
                  width: chartWidth - 200,
                  maxHeight: '400px',
                  scrollBehavior: 'smooth',
                }}
                ref={scrollRef}
              >
                <Line
                  {...props}
                  axisLeft={null}
                  width={X_SCALE_WIDTH * daysInCurrentMonth}
                  margin={{ ...props.margin, left: 0 }}
                />
              </div>
            </div>
          )}
        </div>
      )}
      <AddonCreditsDialog open={addonCreditsDialogOpen} onOpenChange={setAddonCreditsDialogOpen} />
    </div>
  );
};

interface CreditsUsageProps {
  credits: number;
  usage: number;
  daysLeft: number;
}
