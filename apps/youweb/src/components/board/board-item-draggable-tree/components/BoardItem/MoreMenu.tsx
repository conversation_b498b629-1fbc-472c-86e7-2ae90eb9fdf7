import type { BoardGroup } from '@repo/common/types/board-group/types';
import type { ChatDetail } from '@repo/common/types/chat/types';
import type { FavoriteEntityTypeEnum } from '@repo/common/types/favorite/types';
import { DialogTrigger } from '@repo/ui/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@repo/ui/components/ui/dropdown-menu';
import { toast } from '@repo/ui/components/ui/sonner';
import { useTrackActions } from '@repo/ui/lib/posthog/useTrackActions';
import { countTextCharacters } from '@repo/ui-business-editor';
import type { Snip, SnipArticle, SnipImage } from '@repo/ui-business-snip';
import { detectFileType, useSnipDownload } from '@repo/ui-business-snip/hooks/useSnipDownload';
import { atom, useAtom, useAtomValue } from 'jotai';
import {
  Copy,
  Download,
  Ellipsis,
  Loader2,
  SquareArrowOutUpRight,
  SquarePen,
  Star,
  StarOff,
  Trash,
} from 'lucide-react';
import type React from 'react';
import { useState } from 'react';
import { DeleteDoubleCheckDialog } from '@/components/delete-double-check-dialog';
import { GroupIconV2 } from '@/components/group/GroupIconV2';
import GroupEditor from '@/components/group/group-editor';

import { NewSnip } from '@/components/icon/new-snip';
import { NewThought } from '@/components/icon/new-thought';
import { UngroupIcon } from '@/components/icon/ungroup';
import { SnipCreatorDialog } from '@/components/snip/snip-creator-dialog';
import { snipDetailAtom } from '@/hooks/scoped/useSnips';
import { thoughtCurEditorAtom } from '@/hooks/scoped/useThoughts';
import { useBoardItemTree } from '@/hooks/useBoardItemTree';
import { clearPanelDataAtom, createBoardThoughtAtom, panelStateAtom } from '@/hooks/useBoardState';
import { addBoardItemsAndOpenAtom, renamingBreadcrumbItemIdAtom } from '@/hooks/useBoards';
import { useFavorite } from '@/hooks/useFavorite';
import { useTranslation } from '@/hooks/useTranslation';
import { type BoardItem, BoardItemTypeEnum, BoardTreeItem } from '@/typings/board-item';
import type { Thought } from '@/typings/thought';
import { callHTTP } from '@/utils/callHTTP';
import { cn } from '@/utils/utils';
import { BoardGroupSelector } from '../BoardGroupSelector';
import { CopyAndExportOperations } from './CopyAndExportOperations';
import ExportImageModal from './ExportImageModal';

interface Props {
  item: BoardTreeItem;
  openWithAtom?: boolean;
  showOnHover?: boolean;
  className?: string;
  showCopyLink?: boolean;
  showCopyAndExport?: boolean;
  variant?: 'board-tree' | 'board-breadcrumb';
  showWordCount?: boolean;
}

export const openMenuIdAtom = atom<string | null>(null);

export const MoreMenu: React.FC<Props> = (props) => {
  const {
    item,
    openWithAtom = true,
    showOnHover = true,
    className,
    showCopyLink = false,
    showCopyAndExport = false,
    variant = 'board-tree',
    showWordCount = false,
  } = props;

  const isGroup = item.entity_type === BoardItemTypeEnum.boardGroup;

  // 获取和设置当前打开的菜单 ID
  const [openMenuId, setOpenMenuId] = useAtom(openMenuIdAtom);
  const [, setRenamingBreadcrumbItemId] = useAtom(renamingBreadcrumbItemIdAtom);

  const [loading, setLoading] = useState(false);
  const [exportImageModalOpen, setExportImageModalOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const { getEntityFavorite, toggleFavorite } = useFavorite();

  const { trackButtonClick } = useTrackActions();

  const thoughtEditor = useAtomValue(thoughtCurEditorAtom);

  const { downloadFile } = useSnipDownload();

  const isFavorite = getEntityFavorite(item.entity.id);

  const snipDetail = useAtomValue(snipDetailAtom);

  // 判断当前菜单是否打开
  const isMenuOpen = openWithAtom ? openMenuId === item.id : undefined;

  const [, addBoardItemsAndOpen] = useAtom(addBoardItemsAndOpenAtom);
  const [, createBoardThought] = useAtom(createBoardThoughtAtom);
  const [, clearPanelData] = useAtom(clearPanelDataAtom);

  const panelState = useAtomValue(panelStateAtom);

  // 是 thought
  const isThought = item.entity_type === BoardItemTypeEnum.thought;

  // 检测是否为可下载的文件类型（仅对非 Group 类型检测）
  const isDownloadableFile = !isGroup && detectFileType(item.entity as SnipImage) !== null;

  const {
    startRenamingBoardItem,
    ungroup,
    deleteBoardGroup,
    deleteBoardItem,
    updateEditedBoardGroup,
    refreshBoardItemTree,
  } = useBoardItemTree();

  const { t } = useTranslation('Library.BoardGroup');
  const [isMenuHiding, setIsMenuHiding] = useState(false);

  // 当菜单状态变化时，更新全局 Atom
  const handleMenuOpenChange = (open: boolean) => {
    if (!open) {
      setIsMenuHiding(true);
      setTimeout(() => {
        setIsMenuHiding(false);
      }, 300);
    }
    if (!openWithAtom) return;
    if (open) {
      // 打开当前菜单，同时关闭其他菜单（因为全局只有一个 openMenuId）
      setOpenMenuId(item.id!);
    } else {
      // 如果菜单关闭且当前菜单就是之前打开的那个，则设置为 null
      if (openMenuId === item.id) {
        setOpenMenuId(null);
      }
    }
  };

  // @ts-expect-error 类型保护
  const sourceUrl = item.entity?.webpage?.url;

  const getThoughtDisplayName = () => {
    return (item.entity as Thought).title || 'Untitled';
  };

  const renderDeleteItem = () => {
    return (
      <div className="relative w-full h-5">
        <div
          className="absolute top-[-8px] flex gap-2 h-[36px] w-full items-center"
          onClick={(_e) => {
            // 上报埋点
            trackButtonClick('board_item_delete_click', {
              entity_type: item.entity_type,
            });
          }}
        >
          <Trash size={16} />
          {t('delete')}...
        </div>
      </div>
    );
  };

  const afterMaterialCreated = (
    type: BoardItemTypeEnum,
    items: Snip[] | Thought[] | (ChatDetail & { board_item: BoardItem })[],
  ) => {
    if (items.length) {
      const boardItems = items.map((item_) => ({
        ...item_.board_item!,
        parentId: isGroup ? item.id : item.parentId,
        parent_board_group_id: item.board_group_id,
        entity: item_,
        entity_type: type,
      }));
      // 先跳转，避免一闪而过
      addBoardItemsAndOpen(boardItems as BoardItem[], isGroup ? undefined : item.id);
    }
    setOpenMenuId(null);
    setLoading(false);
  };

  const handleNewThought = async () => {
    setLoading(true);
    const data = await createBoardThought(item.board_id, item.board_group_id);
    if (data) {
      afterMaterialCreated(BoardItemTypeEnum.thought, [data]);
    }
  };

  const handleExportImage = () => {
    setExportImageModalOpen(true);
  };

  const renderFavorite = () => {
    return (
      <DropdownMenuItem
        className="cursor-pointer"
        onClick={() => {
          toggleFavorite({
            entity_id: item.entity.id,
            entity_type: item.entity_type as unknown as FavoriteEntityTypeEnum,
            // @ts-expect-error 类型保护
            entity: item.entity,
          });
          // 上报埋点
          trackButtonClick('board_item_favorite_click', {
            target_state: !isFavorite,
            entity_type: item.entity_type,
          });
        }}
      >
        {isFavorite ? <StarOff size={16} /> : <Star size={16} />}
        {isFavorite ? 'Unfavorite' : 'Favorite'}
      </DropdownMenuItem>
    );
  };

  const renderDuplicate = () => {
    // 只给 Thought 支持
    if (item.entity_type !== BoardItemTypeEnum.thought) {
      return null;
    }
    return (
      <DropdownMenuItem
        className="cursor-pointer"
        onClick={(e) => {
          e.preventDefault();
          handleDuplicate();
          // 上报埋点
          trackButtonClick('board_item_duplicate_click', {
            entity_type: item.entity_type,
          });
        }}
      >
        <Copy size={16} />
        {t('duplicate')}
      </DropdownMenuItem>
    );
  };

  const handleDownload = async () => {
    await downloadFile(item.entity as SnipImage);
  };

  const handleDuplicate = async () => {
    setLoading(true);

    try {
      // 调用 duplicate API
      const { data, error } = await callHTTP('/api/v1/boardItem/duplicate', {
        method: 'POST',
        body: {
          board_item_id: item.id,
        },
      });

      if (error) {
        toast(error.message || 'Failed to duplicate item');
        return;
      }

      // 关闭菜单
      setOpenMenuId(null);

      // 服务端返回 BoardItemDto，转换为之前前端需要的格式
      const formattedData = {
        ...data.entity, // 展开 entity 的所有属性
        entity: data.entity,
        entity_type: data.entity_type,
        board_item: {
          id: data.id,
          created_at: data.created_at,
          updated_at: data.updated_at,
          board_id: data.board_id,
          parent_board_group_id: data.parent_board_group_id,
          rank: data.rank,
          snip_id: data.snip_id,
          thought_id: data.thought_id,
          board_group_id: data.board_group_id,
          chat_id: data.chat_id,
          entity_type: data.entity_type,
        },
      };

      // 如果是单个 item，直接塞。如果是 group，await refreshBoardItemTree();
      if (item.entity_type === BoardItemTypeEnum.thought) {
        afterMaterialCreated(BoardItemTypeEnum.thought, [formattedData]);
      } else if (item.entity_type === BoardItemTypeEnum.snip) {
        afterMaterialCreated(BoardItemTypeEnum.snip, [formattedData]);
      } else {
        await refreshBoardItemTree();
      }

      // 显示成功提示
      toast('Item duplicated successfully');

      // 上报埋点
      trackButtonClick('board_item_duplicate_success', {
        entity_type: item.entity_type,
        entity_id: item.entity.id,
      });
    } catch (error) {
      toast('Failed to duplicate item');
      // 上报错误埋点
      trackButtonClick('board_item_duplicate_error', {
        entity_type: item.entity_type,
        entity_id: item.entity.id,
        error: error instanceof Error ? error.message : '未知错误',
      });
    } finally {
      setLoading(false);
    }
  };

  const renderCopyAndExport = () => {
    if (!showCopyAndExport) return null;

    return (
      <CopyAndExportOperations
        item={item}
        snipDetail={snipDetail as unknown as SnipArticle | undefined}
        thoughtEditor={thoughtEditor ?? undefined}
        onExportImage={handleExportImage}
      />
    );
  };

  const renderMenuContent = () => {
    // 是 Group
    if (isGroup) {
      return (
        <>
          <DropdownMenuGroup>
            <DropdownMenuItem
              className="cursor-pointer"
              onClick={(e) => {
                e.preventDefault();
                handleNewThought();
                // 上报埋点
                trackButtonClick('board_item_group_add_thought_click');
              }}
            >
              <NewThought size={16} />
              Thought
            </DropdownMenuItem>

            <SnipCreatorDialog
              boardId={item.board_id}
              boardGroupId={item.board_group_id}
              boardGroupItemId={item.id}
              onFinish={() => {
                setOpenMenuId(null);
                setLoading(false);
              }}
              autoChangePanelData
            >
              <DropdownMenuItem
                onClick={(e) => {
                  e.preventDefault();
                  // 上报埋点
                  trackButtonClick('board_item_group_add_snip_click');
                }}
                className="cursor-pointer"
              >
                <DialogTrigger className="flex items-center w-full gap-2">
                  <NewSnip size={16} />
                  Snip
                </DialogTrigger>
              </DropdownMenuItem>
            </SnipCreatorDialog>

            <DropdownMenuSeparator />

            <GroupEditor
              boardItem={item}
              variant="edit"
              onGroupChange={(newItem) => {
                updateEditedBoardGroup({
                  groupId: item.entity.id,
                  name: newItem.name,
                  icon: newItem.icon,
                });
                // 关闭浮层
                handleMenuOpenChange(false);
              }}
            >
              <DropdownMenuItem
                className="p-0 cursor-pointer"
                onClick={(e) => {
                  e.preventDefault();
                  // 上报埋点
                  trackButtonClick('board_item_group_edit_click');
                }}
              >
                <DialogTrigger asChild>
                  <div className="flex items-center w-full h-full gap-2 p-2">
                    <SquarePen size={16} />
                    {t('edit')}
                  </div>
                </DialogTrigger>
              </DropdownMenuItem>
            </GroupEditor>

            {renderFavorite()}

            {renderDuplicate()}

            <DropdownMenuItem
              className="cursor-pointer"
              onClick={(_e) => {
                ungroup(item.entity.id);
                // 上报埋点
                trackButtonClick('board_item_ungroup_click', {
                  board_group_id: item.entity.id,
                });
              }}
            >
              <UngroupIcon size={16} />
              {t('ungroup')}
            </DropdownMenuItem>
          </DropdownMenuGroup>

          <DropdownMenuItem
            onClick={(e) => {
              e.preventDefault();
              if (!item.children.length) {
                deleteBoardGroup(item);
              }
            }}
            className="cursor-pointer"
          >
            {item.children.length ? (
              <DeleteDoubleCheckDialog
                header={
                  <div className="flex items-center justify-center w-6 h-6 ml-2 scale-150">
                    <GroupIconV2 icon={(item.entity as unknown as BoardGroup).icon} />
                  </div>
                }
                // @ts-expect-error 没事没事
                title={`Delete "${item.entity?.name}" group?`}
                deleteButtonText="Delete"
                description={`This group and all of its ${item.children.length} ${
                  item.children.length === 1 ? 'item' : 'items'
                } will be deleted and cannot be recovered.`}
                onConfirm={() => {
                  deleteBoardGroup(item);
                  // 上报埋点
                  trackButtonClick('board_item_group_delete_confirm_click');
                }}
              >
                {renderDeleteItem()}
              </DeleteDoubleCheckDialog>
            ) : (
              renderDeleteItem()
            )}
          </DropdownMenuItem>
        </>
      );
    }
    // 非 Group
    return (
      <>
        <DropdownMenuGroup>
          <DropdownMenuItem
            className="flex cursor-pointer"
            onClick={(_e) => {
              if (variant === 'board-tree') {
                setTimeout(() => {
                  startRenamingBoardItem(item.id);
                }, 100);
              }
              if (variant === 'board-breadcrumb') {
                // 使用面包屑重命名功能
                setTimeout(() => {
                  setRenamingBreadcrumbItemId(item.id);
                }, 100);
                handleMenuOpenChange(false);
              }
              // 上报埋点
              trackButtonClick('board_item_rename_click', {
                entity_type: item.entity_type,
              });
            }}
          >
            <SquarePen size={16} />
            {t('rename')}
          </DropdownMenuItem>

          {renderFavorite()}

          {showCopyLink && sourceUrl && (
            <a
              href={sourceUrl}
              target="_blank"
              onClick={(_e) => {
                // 上报埋点
                trackButtonClick('board_item_open_source_click', {
                  entity_type: item.entity_type,
                  webpage_url: sourceUrl,
                });
              }}
            >
              <DropdownMenuItem className="cursor-pointer">
                <SquareArrowOutUpRight size={16} />
                {t('openSource')}
              </DropdownMenuItem>
            </a>
          )}

          <BoardGroupSelector
            item={item}
            closeTopMenu={() => {
              handleMenuOpenChange(false);
            }}
          />
        </DropdownMenuGroup>

        {isDownloadableFile && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="cursor-pointer" onClick={handleDownload}>
              <Download size={16} />
              {t('download')}
            </DropdownMenuItem>
          </>
        )}

        {renderDuplicate()}

        {renderCopyAndExport()}

        {isThought ? (
          <DeleteDoubleCheckDialog
            open={deleteDialogOpen}
            onOpenChange={setDeleteDialogOpen}
            title={`Delete "${getThoughtDisplayName()}" thought?`}
            description="This thought will be deleted and cannot be recovered."
            deleteButtonText={t('delete')}
            onConfirm={() => {
              deleteBoardItem(item.id!);
              if (panelState.panelData?.id === item.id) {
                clearPanelData();
              }
              trackButtonClick('board_item_delete_confirm_click', {
                entity_type: item.entity_type,
              });
            }}
          >
            <DropdownMenuItem
              className="cursor-pointer"
              onClick={(e) => {
                e.preventDefault();
                setDeleteDialogOpen(true);
              }}
            >
              <Trash size={16} />
              {t('delete')}
            </DropdownMenuItem>
          </DeleteDoubleCheckDialog>
        ) : (
          <DropdownMenuItem
            className="cursor-pointer"
            onClick={(_e) => {
              deleteBoardItem(item.id!);
              if (panelState.panelData?.id === item.id) {
                clearPanelData();
              }
            }}
          >
            <Trash size={16} />
            {t('delete')}
          </DropdownMenuItem>
        )}
      </>
    );
  };
  return (
    <DropdownMenu open={isMenuOpen} onOpenChange={handleMenuOpenChange} modal={false}>
      <DropdownMenuTrigger>
        <div
          className={cn(
            'rounded-[4px] p-1 hover:bg-muted',
            showOnHover ? 'hidden group-hover:block' : '',
            (isMenuOpen || isMenuHiding) && 'block',
            className,
          )}
        >
          <Ellipsis size={16} className="text-secondary-fg" />
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="relative w-[220px] rounded-xl border-none p-2" align="start">
        {loading && (
          <>
            <Loader2
              size={16}
              className="absolute z-30 right-2 top-2 animate-spin text-secondary-fg"
            />
            <div className="absolute inset-0 z-20 bg-background/40" />
          </>
        )}
        {renderMenuContent()}
        {showWordCount && isThought && thoughtEditor && (
          <>
            <DropdownMenuSeparator />
            <div className="p-2 text-sm text-secondary-fg">
              <p className="truncate">
                Word count: {countTextCharacters(thoughtEditor.getText())} words
              </p>
            </div>
          </>
        )}
      </DropdownMenuContent>

      {exportImageModalOpen && (
        <ExportImageModal
          open={exportImageModalOpen}
          onOpenChange={setExportImageModalOpen}
          selector={() => {
            if (item.entity_type === BoardItemTypeEnum.thought) {
              return thoughtEditor?.view?.dom as HTMLDivElement;
            }
            return document.querySelector('.ym-reader-container') as HTMLDivElement;
          }}
          title={'title' in item.entity ? item.entity.title || 'Untitled' : 'Untitled'}
        />
      )}
    </DropdownMenu>
  );
};
