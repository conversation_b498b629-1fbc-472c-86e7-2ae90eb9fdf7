import { defaultsDeep } from 'lodash-es';
import { forwardRef } from 'react';

import ExportImagePreview, { ExportImagePreviewProps, ExportImagePreviewRef } from './base';

// 样式主题枚举
export type StyleTheme = 'clean' | 'parchment' | 'ink';

// 扩展Props接口，添加主题支持
export interface ThemedExportImagePreviewProps extends ExportImagePreviewProps {
  theme?: StyleTheme;
}

// 预设主题样式配置
const themeConfigs = {
  clean: {
    classNames: {
      wrapper: 'bg-card',
      title: 'text-2xl font-semibold text-foreground',
      content: 'text-muted-foreground leading-relaxed',
      footer: 'bg-card text-muted-foreground text-sm',
      watermark: 'text-border',
      headerDivider: 'border-divider',
      footerDivider: 'border-divider',
      header: 'flex justify-end text-border',
    },
    style: {
      // background:
      //   "linear-gradient(to bottom, #ffffff 0%, #ffffff 95%, #ECF1F9)",
    },
    props: {
      watermarkOptions: {
        show: true,
        type: 'logo-text' as const,
        position: 'top-left' as const,
        size: 16,
      },
    },
  },
  parchment: {
    classNames: {
      wrapper: 'bg-card',
      title: 'text-2xl font-bold text-foreground pb-4',
      content: 'text-foreground leading-relaxed',
      footer: 'bg-card text-muted-foreground text-sm',
      watermark: 'text-disabled',
      headerDivider: 'border-divider',
      footerDivider: 'border-divider',
    },
    style: {},
    props: {},
  },
  ink: {
    classNames: {
      wrapper: 'bg-card text-foreground',
      title: 'text-2xl font-bold text-foreground pb-4',
      content: 'text-foreground leading-relaxed',
      footer: 'bg-card text-muted-foreground text-sm',
      watermark: 'text-border',
      headerDivider: 'border-divider',
      footerDivider: 'border-divider',
    },
    style: {},
    props: {},
  },
};

const ThemedExportImagePreview = forwardRef<ExportImagePreviewRef, ThemedExportImagePreviewProps>(
  ({ theme = 'clean', classNames = {}, style = {}, ...props }, ref) => {
    const themeConfig = themeConfigs[theme];

    // 合并主题样式和自定义样式
    const mergedClassNames = {
      ...themeConfig.classNames,
      ...classNames,
    };

    const mergedStyle = {
      ...themeConfig.style,
      ...style,
    };

    const mergedProps = defaultsDeep(props, themeConfig.props);

    return (
      <ExportImagePreview
        ref={ref}
        {...mergedProps}
        classNames={mergedClassNames}
        style={mergedStyle}
      />
    );
  },
);

ThemedExportImagePreview.displayName = 'ThemedExportImagePreview';

// 导出主题化组件作为默认导出
export default ThemedExportImagePreview;

// 导出类型
export type {
  ClassNames,
  ExportImagePreviewProps,
  ExportImagePreviewRef,
  ExportOptions,
  WatermarkOptions,
  WatermarkPosition,
  WatermarkType,
} from './base';
// 同时导出基础组件，以备需要完全自定义的场景
export { default as BaseExportImagePreview } from './base';

// 预设样式示例组件
export const ExportImagePreviewClean = forwardRef<
  ExportImagePreviewRef,
  Omit<ThemedExportImagePreviewProps, 'theme'>
>((props, ref) => <ThemedExportImagePreview ref={ref} {...props} theme="clean" />);

export const ExportImagePreviewParchment = forwardRef<
  ExportImagePreviewRef,
  Omit<ThemedExportImagePreviewProps, 'theme'>
>((props, ref) => <ThemedExportImagePreview ref={ref} {...props} theme="parchment" />);

export const ExportImagePreviewInk = forwardRef<
  ExportImagePreviewRef,
  Omit<ThemedExportImagePreviewProps, 'theme'>
>((props, ref) => <ThemedExportImagePreview ref={ref} {...props} theme="ink" />);

ExportImagePreviewClean.displayName = 'ExportImagePreviewClean';
ExportImagePreviewParchment.displayName = 'ExportImagePreviewParchment';
ExportImagePreviewInk.displayName = 'ExportImagePreviewInk';
