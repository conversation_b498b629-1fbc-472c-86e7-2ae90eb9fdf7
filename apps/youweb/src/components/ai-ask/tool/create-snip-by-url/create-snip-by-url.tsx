import { SnipType } from '@repo/api/generated-client/snake-case/index';
import { TOOL_TYPES } from '@repo/common/consts/tool/const';
import { BoardItemTypeEnum } from '@repo/common/types/board-item/types';
import { MessageStatusEnum } from '@repo/common/types/chat/enum';
import {
  SnipImage,
  SnipPDF,
  SnipSnippet,
  SnipTypeEnum,
  SnipUnknownWebpage,
  SnipVoice,
} from '@repo/ui-business-snip';
import { useAtom } from 'jotai';
import { useEffect, useState } from 'react';
import { NewSnip } from '@/components/icon/new-snip';
import { boardDetailAtom, refreshBoardDetail<PERSON>tom } from '@/hooks/useBoards';
import { CompletionBlockStatusEnum, CompletionToolBlock } from '@/typings/completion-block';
import {
  AUDIO_ICON,
  IMAGE_ICON,
  PDF_ICON,
  SNIPPET_ICON,
  THOUGHT_ICON,
  WEBSITE_ICON,
} from '@/utils/board/getImageAndTitleFromBoardItem';
import { ToolStatus, ToolTitle } from '../card';
import { useAddBoardItemsWhenBlockRunning } from '../hooks/useAddSnipResultToBoard';
import { useMessage } from '../hooks/useMessage';
import { useSearchedData } from '../hooks/useSearchedData';
import { TOOL_RENDERER } from '../type';
import { checkTitleValid } from '../utils/checkTitleValid';
import { ImageItem } from './ImageItem';
import { TriggerBubble } from './TriggerBubble';
import { WebpageItem } from './WebpageItem';

const renderCardTitle = (block: CompletionToolBlock) => {
  if (block.status === CompletionBlockStatusEnum.errored) {
    return 'Failed to create snips';
  }
  if (block.status !== CompletionBlockStatusEnum.success) {
    return 'Creating snips';
  }
  if (!block.tool_result?.snipsResults) {
    return 'Failed to create snips';
  }
  const successfulSnips = block.tool_result?.snipsResults?.filter(
    (snipResult: any) => snipResult.success && snipResult.snip?.id,
  );
  const createdSnipCount = successfulSnips?.length;
  if (createdSnipCount === 0) {
    return 'Failed to create snips';
  }
  return `Created snips`;
};

export const CreateSnipByUrlCard = ({
  block,
  variant = 'small',
}: {
  block: CompletionToolBlock;
  variant: 'small' | 'middle';
}) => {
  const [, refreshBoardDetail] = useAtom(refreshBoardDetailAtom);
  const message = useMessage();
  const [boardDetail] = useAtom(boardDetailAtom);

  const [isOpen, setIsOpen] = useState(true);

  const { getSearchedDataByUrl } = useSearchedData();

  useEffect(() => {
    if (message?.status !== MessageStatusEnum.ING) {
      return;
    }
    if (block.status !== CompletionBlockStatusEnum.success) return;
    // 看看要不要刷新咱们 board
    const firstSnip = block.tool_result?.snipsResults?.[0];
    if (!firstSnip) return;
    if (firstSnip.board_id === boardDetail?.id) {
      refreshBoardDetail();
    }
  }, [block.status, message?.status]);

  useAddBoardItemsWhenBlockRunning(block, (block) => {
    const successfulSnips =
      block?.tool_result?.snipsResults?.filter(
        (snipResult: any) => snipResult?.success && snipResult?.snip?.id,
      ) || [];

    if (successfulSnips.length === 0) return [];

    // 为每个新 snip 创建 BoardItem 结构
    const newBoardItems = successfulSnips.map((snipResult: any) => {
      const snip = snipResult.snip!;
      if (snipResult.type === 'thought') {
        return {
          id: snip.board_item?.id || snip.id,
          created_at: snip.board_item?.created_at || new Date(),
          updated_at: snip.board_item?.updated_at || new Date(),
          board_id: snip.board_item?.board_id || boardDetail!.id,
          thought_id: snip.id,
          snip_id: undefined,
          chat_id: undefined,
          board_group_id: undefined,
          parent_board_group_id: undefined,
          rank: snip.board_item?.rank || '',
          entity_type: BoardItemTypeEnum.THOUGHT,
          entity: snip,
        };
      }
      return {
        id: snip.board_item?.id || snip.id,
        created_at: snip.board_item?.created_at || new Date(),
        updated_at: snip.board_item?.updated_at || new Date(),
        board_id: snip.board_item?.board_id || boardDetail!.id,
        rank: snip.board_item?.rank || '',
        snip_id: snip.id,
        thought_id: undefined,
        chat_id: undefined,
        board_group_id: undefined,
        entity_type: BoardItemTypeEnum.SNIP,
        entity: snip,
      };
    });
    return newBoardItems;
  });

  const snipResults: any[] =
    block.tool_result?.snipsResults?.filter(
      (snipResult: any) => snipResult.success && snipResult.snip?.id,
    ) || [];

  const successSnipResults = snipResults.filter(
    (snipResult) => snipResult.success && snipResult.snip?.id,
  );

  const renderSiteName = (snipResult: any) => {
    if (snipResult.type === 'thought') {
      return 'Thought';
    }
    if (snipResult.snip?.type === SnipType.pdf) {
      return 'PDF';
    }
    if ((snipResult.snip as SnipImage)?.type === SnipTypeEnum.image) {
      return 'Image';
    }
    if ((snipResult.snip as SnipSnippet)?.type === SnipTypeEnum.snippet) {
      return 'Snippet';
    }
    if ((snipResult.snip as SnipVoice)?.type === SnipTypeEnum.voice) {
      return 'Voice';
    }
    return (snipResult.snip as SnipUnknownWebpage)?.webpage?.site?.name || 'Webpage';
  };

  const renderTitle = (snipResult: any) => {
    // 首先检查是否有用户或AI提供的标题
    if (snipResult.provided_title && checkTitleValid(snipResult.provided_title)) {
      return snipResult.provided_title;
    }

    // 如果是 thought
    if (snipResult.type === 'thought') {
      return snipResult.snip?.title || snipResult.url;
    }
    const title = snipResult.snip?.title || (snipResult.snip as SnipUnknownWebpage)?.webpage?.title;
    if (title && checkTitleValid(title)) {
      return title;
    }
    // 返回 web search 那边的值
    const searchedData = getSearchedDataByUrl(snipResult.url);
    if (searchedData && checkTitleValid(searchedData.title)) {
      return searchedData.title;
    }
    return snipResult.url;
  };

  const getFavicon = (snipResult: any) => {
    // 如果是 thought
    if (snipResult.type === 'thought') {
      return THOUGHT_ICON;
    }
    // 如果是 image
    if ((snipResult.snip as SnipImage)?.type === SnipTypeEnum.image) {
      const imageUrl = (snipResult.snip as SnipImage)?.file?.url;
      if (imageUrl) {
        return imageUrl;
      }
      return IMAGE_ICON;
    }
    // 如果是 voice
    if ((snipResult.snip as SnipVoice)?.type === SnipTypeEnum.voice) {
      if (snipResult.provided_album_url) {
        return snipResult.provided_album_url;
      }
      return AUDIO_ICON;
    }
    // 如果是 snippet
    if ((snipResult.snip as SnipSnippet)?.type === SnipTypeEnum.snippet) {
      return SNIPPET_ICON;
    }
    // 如果是 pdf
    if ((snipResult.snip as SnipPDF)?.type === SnipTypeEnum.pdf) {
      return PDF_ICON;
    }
    if ((snipResult.snip as SnipUnknownWebpage)?.webpage?.site?.favicon_url) {
      return (snipResult.snip as SnipUnknownWebpage)?.webpage?.site?.favicon_url;
    }
    // 返回 web search 那边的值
    const searchedData = getSearchedDataByUrl(snipResult.url);
    if (searchedData?.favicon) {
      return searchedData.favicon;
    }
    return WEBSITE_ICON;
  };

  const renderLabel = () => {
    const variant = getVariant();
    // 如果所有 snip 都是 image
    if (variant === 'image') {
      return successSnipResults.length > 1 ? 'images added' : 'image added';
    }
    // 如果所有 snip 都是 audio
    if (variant === 'audio') {
      return successSnipResults.length > 1 ? 'audios added' : 'audio added';
    }
    return successSnipResults.length > 1 ? 'snips added' : 'snip added';
  };

  const getVariant = () => {
    if (successSnipResults.every((snipResult) => snipResult.snip?.type === SnipTypeEnum.image)) {
      return 'image';
    }
    if (successSnipResults.every((snipResult) => snipResult.snip?.type === SnipTypeEnum.voice)) {
      return 'audio';
    }
    return 'webpage';
  };

  const renderSnipResults = () => {
    if (successSnipResults.length === 0) {
      return null;
    }
    if (!isOpen) {
      return (
        <div className="mt-[10px]">
          <TriggerBubble
            sources={successSnipResults}
            getFavicon={getFavicon}
            onClick={() => setIsOpen(true)}
            label={renderLabel()}
            variant={getVariant()}
          />
        </div>
      );
    }
    return (
      <div className="mt-[10px] w-full overflow-hidden rounded-[16px] border border-muted bg-card pb-2">
        <div
          className="flex h-8 cursor-pointer items-center border-b border-muted px-4 py-[6px]"
          onClick={() => setIsOpen(false)}
        >
          <div className="text-xs font-normal text-caption-fg">
            {successSnipResults.length} {renderLabel()}
          </div>
        </div>
        <div className="mt-2 flex flex-col gap-4 px-4 py-2">
          {successSnipResults.map((snipResult) => {
            if (snipResult.snip?.type === SnipTypeEnum.image) {
              return (
                <ImageItem
                  key={snipResult.snip?.id}
                  snipResult={snipResult}
                  getFavicon={getFavicon}
                  renderSiteName={renderSiteName}
                  renderTitle={renderTitle}
                />
              );
            }
            if (snipResult.snip?.type === SnipTypeEnum.voice) {
              return (
                <ImageItem
                  key={snipResult.snip?.id}
                  snipResult={snipResult}
                  getFavicon={getFavicon}
                  renderSiteName={renderSiteName}
                  renderTitle={renderTitle}
                />
              );
            }
            return (
              <WebpageItem
                key={snipResult.snip?.id}
                snipResult={snipResult}
                getFavicon={getFavicon}
                renderSiteName={renderSiteName}
                renderTitle={renderTitle}
              />
            );
          })}
        </div>
      </div>
    );
  };
  return (
    <div>
      <ToolTitle
        text={renderCardTitle(block)}
        icon={<NewSnip size={14} className="mt-[1px] shrink-0" />}
        variant={variant}
        blockStatus={block.status}
      />
      {renderSnipResults()}
    </div>
  );
};

export const CreateSnipByUrlTool: TOOL_RENDERER = {
  type: TOOL_TYPES.CREATE_SNIP_BY_URL,
  renderer: CreateSnipByUrlCard,
  needRefreshBoard: true,
  getToolTitle: (block) => {
    return <ToolStatus logo={<NewSnip size={16} />} commandName={renderCardTitle(block)} />;
  },
};
