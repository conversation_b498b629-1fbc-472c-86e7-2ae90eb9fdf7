import { MessageCreateSingleUrlResult } from '@repo/common/types/chat/enum';

import { SnipUnknownWebpageVO } from '@repo/common/types/snip/app-types';
import { SnipTypeEnum } from '@repo/common/types/snip/types';
import { Button } from '@repo/ui/components/ui/button';
import { useEffect, useState } from 'react';
import { WEBSITE_ICON } from '@/utils/board/getImageAndTitleFromBoardItem';

import { useSnipItemAction } from './useSnipItemAction';

interface WebpageItemProps {
  snipResult: MessageCreateSingleUrlResult;
  getFavicon: (snipResult: MessageCreateSingleUrlResult) => string;
  renderSiteName: (snipResult: MessageCreateSingleUrlResult) => string;
  renderTitle: (snipResult: MessageCreateSingleUrlResult) => string;
}

export const ImageItem = ({
  snipResult,
  getFavicon,
  renderSiteName,
  renderTitle,
}: WebpageItemProps) => {
  const { canOpen, handleOpen } = useSnipItemAction(snipResult);

  const [audioTime, setAudioTime] = useState('00:00');

  const snip = snipResult.snip;

  useEffect(() => {
    if (audioTime !== '00:00') {
      return;
    }
    if (snipResult.snip?.type === SnipTypeEnum.VOICE) {
      const url = snipResult.url;
      // 获取音频时长
      const audio = new Audio(url);
      audio.onloadedmetadata = () => {
        // 将秒数转换为 MM:SS 格式
        const formatTime = (seconds: number): string => {
          const totalSeconds = Math.ceil(seconds);
          const minutes = Math.floor(totalSeconds / 60);
          const remainingSeconds = totalSeconds % 60;
          return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
        };
        setAudioTime(formatTime(audio.duration));
      };
    }
  }, [snipResult]);

  const renderSubtitle = () => {
    if (snipResult.snip?.type === SnipTypeEnum.VOICE) {
      return audioTime;
    }
    return renderSiteName(snipResult);
  };

  if (!snip || !snip.board_item?.id) {
    return null;
  }

  return (
    <div key={snip?.id} className="group relative flex w-full items-center justify-between gap-3">
      <img
        src={getFavicon(snipResult)}
        alt={(snip as SnipUnknownWebpageVO)?.webpage?.site?.name}
        className="h-10 w-10 flex-shrink-0 rounded-[5px] object-cover"
        onError={(e) => {
          e.currentTarget.src = WEBSITE_ICON;
        }}
      />

      <div className="flex flex-1 flex-col gap-0.5">
        <div className="line-clamp-1 text-sm font-medium text-foreground">
          {renderTitle(snipResult)}
        </div>
        <div className="line-clamp-1 text-xs text-caption-fg">{renderSubtitle()}</div>
      </div>

      <div className="hidden h-full items-center group-hover:flex bg-[linear-gradient(to_right,transparent_0px,var(--card)_10px,var(--card)_100%)] pl-3">
        {canOpen && (
          <Button variant="outline" size="sm" onClick={handleOpen}>
            Open
          </Button>
        )}
      </div>
    </div>
  );
};
