import { BoardItemTypeEnum } from '@repo/common/types/board-item/types';
import type { MessageCreateSingleUrlResult } from '@repo/common/types/chat';
import { useAtomValue, useSetAtom } from 'jotai';
import { useParams } from 'react-router-dom';
import { changePanelDataAtom, useEnterBoardWorkSpace } from '@/hooks/useBoardState';
import { boardDetailAtom } from '@/hooks/useBoards';

export const useSnipItemAction = (snipResult: MessageCreateSingleUrlResult) => {
  const board = useAtomValue(boardDetailAtom);
  const params = useParams();
  const changePanelData = useSetAtom(changePanelDataAtom);

  const enterBoardWorkSpace = useEnterBoardWorkSpace();

  const snip = snipResult.snip;

  if (!snip || !snip.board_item?.id) {
    return {
      canOpen: false,
      handleOpen: () => {
        // do nothing
      },
    };
  }

  const isInNewBoard = params?.slug === 'new';

  const canOpen = !isInNewBoard && snip?.board_item?.board_id;

  const handleOpen = () => {
    if (!snip.board_item?.id || !board) return;

    const isInCurrentBoardDetail = snip.board_item.id === board.id;
    // 如果不在当前 board 里，则跳转到对应 board 的详情页
    if (!isInCurrentBoardDetail) {
      enterBoardWorkSpace(
        snip.board_item.board_id,
        (snipResult.type as BoardItemTypeEnum) || BoardItemTypeEnum.SNIP,
        snip.id,
      );
    } else {
      changePanelData({
        id: snip.board_item.id,
        entity: snip,
        entity_type: BoardItemTypeEnum.SNIP,
      });
    }
  };

  return { canOpen, handleOpen };
};
