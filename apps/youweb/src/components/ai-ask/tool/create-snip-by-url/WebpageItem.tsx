import { MessageCreateSingleUrlResult } from '@repo/common/types/chat/enum';
import { SnipUnknownWebpageVO } from '@repo/common/types/snip/app-types';
import { Button } from '@repo/ui/components/ui/button';
import { WEBSITE_ICON } from '@/utils/board/getImageAndTitleFromBoardItem';

import { useSnipItemAction } from './useSnipItemAction';

interface WebpageItemProps {
  snipResult: MessageCreateSingleUrlResult;
  getFavicon: (snipResult: MessageCreateSingleUrlResult) => string;
  renderSiteName: (snipResult: MessageCreateSingleUrlResult) => string;
  renderTitle: (snipResult: MessageCreateSingleUrlResult) => string;
}

export const WebpageItem = ({
  snipResult,
  getFavicon,
  renderSiteName,
  renderTitle,
}: WebpageItemProps) => {
  const { canOpen, handleOpen } = useSnipItemAction(snipResult);

  const snip = snipResult.snip;

  if (!snip?.id) {
    return null;
  }

  return (
    <div key={snip?.id} className="group relative flex flex-col gap-1">
      <div className="flex items-center gap-1">
        <img
          src={getFavicon(snipResult)}
          alt={(snip as SnipUnknownWebpageVO)?.webpage?.site?.name}
          className="h-4 w-4"
          onError={(e) => {
            e.currentTarget.src = WEBSITE_ICON;
          }}
        />
        <div className="line-clamp-1 text-xs">{renderSiteName(snipResult)}</div>
      </div>
      <div className="line-clamp-2 text-sm font-medium">{renderTitle(snipResult)}</div>
      {(snip as SnipUnknownWebpageVO)?.webpage?.description && (
        <div className="line-clamp-2 text-xs font-normal leading-[18px] text-caption-fg">
          {(snip as SnipUnknownWebpageVO)?.webpage?.description}
        </div>
      )}
      <div className="absolute right-0 top-0 hidden h-full items-center pt-1 group-hover:flex bg-[linear-gradient(to_right,transparent_0px,var(--card)_10px,var(--card)_100%)] pl-3">
        {canOpen && (
          <Button variant="outline" size="sm" onClick={handleOpen}>
            Open
          </Button>
        )}
      </div>
    </div>
  );
};
