import { WEBSITE_ICON } from '@/utils/board/getImageAndTitleFromBoardItem';
import { cn } from '@/utils/utils';

// 更贴合图片样式的 Trigger 组件
export function TriggerBubble<T>({
  sources,
  onClick,
  getFavicon,
  label = 'sources',
  variant = 'webpage',
}: {
  sources: T[];
  onClick: () => void;
  getFavicon: (source: T) => string | React.ReactNode;
  label?: string;
  variant?: 'webpage' | 'image' | 'audio';
}) {
  // 只取前3个源用于显示 favicon
  const showSources = sources.slice(0, 3);

  const imageVariant = variant === 'image' || variant === 'audio';

  const renderIcons = () => {
    return showSources.map((r, index) => {
      const favicon = getFavicon(r);
      return typeof favicon === 'string' ? (
        <img
          key={index}
          src={favicon}
          alt="favicon"
          className={cn(
            'box-content h-[14px] w-[14px] rounded-full border-[1px] border-[#F5F5F6] bg-card object-cover',
            imageVariant && 'rounded-[4px]',
          )}
          style={{
            marginLeft: index === 0 ? 0 : -4,
            zIndex: showSources.length - index,
          }}
          onError={(e) => {
            e.currentTarget.src = WEBSITE_ICON;
          }}
        />
      ) : (
        favicon
      );
    });
  };

  return (
    <div
      className={cn(
        'font-xs flex h-8 w-fit max-w-full cursor-pointer flex-row items-center rounded-full border border-muted bg-card px-3 font-normal text-foreground',
      )}
      onClick={onClick}
    >
      <div className="flex flex-row items-center gap-1">
        <div className="flex flex-row">{renderIcons()}</div>
        {/* 右侧显示总数和类型，英文文案 */}
        <span className="text-[13px]">{`${sources.length} ${label}`}</span>
      </div>
    </div>
  );
}
