import { TOOL_TYPES } from '@repo/common/consts/tool/const';
import { ExternalLink, Globe, Image as ImageIcon } from 'lucide-react';
import { CompletionBlockStatusEnum, CompletionToolBlock } from '@/typings/completion-block';
import { ToolStatus, ToolTitle } from './card';
import type { TOOL_RENDERER, ToolRendererProps } from './type';

// Define types for prefetch results
interface UrlMetadata {
  url: string;
  title?: string;
  description?: string;
  ogImage?: string;
  ogTitle?: string;
  ogDescription?: string;
  favicon?: string;
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  error?: string;
}

interface PrefetchUrlsToolResult {
  metadataList: UrlMetadata[];
}

function getHostName(url: string) {
  try {
    return new URL(url).hostname;
  } catch (_error) {
    return url;
  }
}

function UrlMetadataCard({ metadata }: { metadata: UrlMetadata }) {
  const displayTitle = metadata.ogTitle || metadata.title || metadata.url;
  const displayDescription = metadata.ogDescription || metadata.description;
  const displayImage = metadata.ogImage;
  const hostname = getHostName(metadata.url);

  if (metadata.error) {
    return (
      <div className="group relative rounded-lg border border-destructive/20 bg-destructive/5 p-3">
        <div className="flex items-start gap-3">
          <div className="mt-1 flex-shrink-0">
            <Globe className="h-4 w-4 text-destructive" />
          </div>
          <div className="min-w-0 flex-1">
            <div className="truncate text-sm font-medium text-destructive">{metadata.url}</div>
            <div className="mt-1 text-xs text-destructive/80">Error: {metadata.error}</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className="group relative cursor-pointer rounded-[16px] border border-muted bg-card p-4 transition-colors hover:bg-accent/50"
      onClick={() => window.open(metadata.url, '_blank')}
    >
      <div className="flex items-start gap-3">
        {displayImage ? (
          <div className="flex-shrink-0">
            <img
              src={displayImage}
              alt={displayTitle}
              className="h-20 w-20 rounded object-cover"
              onError={(e) => {
                // 如果图片加载失败，显示默认图标
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                const fallback = target.nextElementSibling as HTMLElement;
                if (fallback) fallback.style.display = 'flex';
              }}
            />
            <div className="hidden h-12 w-12 items-center justify-center rounded bg-muted">
              <ImageIcon className="h-5 w-5 text-muted-foreground" />
            </div>
          </div>
        ) : (
          <div className="flex-shrink-0">
            <div className="flex h-12 w-12 items-center justify-center rounded bg-muted">
              <Globe className="h-5 w-5 text-muted-foreground" />
            </div>
          </div>
        )}

        <div className="min-w-0 flex-1">
          <div className="line-clamp-2 text-sm font-medium text-foreground">{displayTitle}</div>
          {displayDescription && (
            <div className="mt-1 line-clamp-2 text-xs text-muted-foreground">
              {displayDescription}
            </div>
          )}
          <div className="mt-2 flex items-center gap-2">
            {metadata.favicon && (
              <img
                src={metadata.favicon}
                alt=""
                className="h-3 w-3"
                onError={(e) => {
                  (e.target as HTMLImageElement).style.display = 'none';
                }}
              />
            )}
            <span className="text-xs text-muted-foreground">{hostname}</span>
            {metadata.author && (
              <>
                <span className="text-xs text-muted-foreground">•</span>
                <span className="text-xs text-muted-foreground">{metadata.author}</span>
              </>
            )}
          </div>
        </div>

        <div className="flex-shrink-0">
          <ExternalLink className="h-4 w-4 text-muted-foreground" />
        </div>
      </div>
    </div>
  );
}

export const PrefetchUrlsCard = (props: ToolRendererProps) => {
  const { block, variant } = props as ToolRendererProps<PrefetchUrlsToolResult>;
  const metadataList = (block.tool_result?.metadataList || []) as UrlMetadata[];
  const urls = ((block.tool_arguments as Record<string, unknown>)?.urls as string[]) || [];

  const { commandName, commandDescription } = renderTitle(block, urls);

  // Display different content based on status
  if (block.status === CompletionBlockStatusEnum.generating) {
    return (
      <div>
        <ToolTitle
          text={
            <>
              <span>{commandName}</span>
              {commandDescription && (
                <span className="ml-2 text-xs text-caption-fg">{commandDescription}</span>
              )}
            </>
          }
          variant={variant}
          icon={<Globe size={14} />}
          blockStatus={block.status}
          classNames={{
            text: 'truncate',
          }}
        />
      </div>
    );
  }

  return (
    <div>
      <ToolTitle
        text={
          <>
            <span>{commandName}</span>
            {commandDescription && (
              <span className="ml-2 text-xs text-caption-fg">{commandDescription}</span>
            )}
          </>
        }
        variant={variant}
        icon={<Globe size={14} />}
        blockStatus={block.status}
        classNames={{
          text: 'truncate',
        }}
      />

      {/* Display fetch results */}
      {metadataList.length > 0 && (
        <div className="mt-2">
          <div className="space-y-2">
            {metadataList.map((metadata, index) => (
              <UrlMetadataCard key={index} metadata={metadata} />
            ))}
          </div>

          {/* Display success statistics */}
          {/* <div className="mt-3 text-xs text-muted-foreground">
            Successfully fetched {metadataList.filter((m) => !m.error).length} /{' '}
            {metadataList.length} URLs
          </div> */}
        </div>
      )}
    </div>
  );
};

const renderTitle = (block: CompletionToolBlock, urls: string[]) => {
  const urlCount = urls?.length || 0;

  // If in generating status
  if (block.status === CompletionBlockStatusEnum.generating) {
    return {
      commandName: `Prefetching ${urlCount} URLs`,
      commandDescription: undefined,
    };
  }

  // If execution completed
  return {
    commandName: `Prefetched ${urlCount} URLs`,
    commandDescription: urls?.[0] && urlCount === 1 ? getHostName(urls[0]) : undefined,
  };
};

export const PrefetchUrlsTool: TOOL_RENDERER = {
  type: TOOL_TYPES.PREFETCH_URLS,
  renderer: PrefetchUrlsCard,
  getToolTitle: (block) => {
    const urls = ((block.tool_arguments as Record<string, unknown>)?.urls as string[]) || [];
    const { commandName, commandDescription } = renderTitle(block, urls);
    return (
      <ToolStatus
        logo={<Globe size={16} />}
        commandName={commandName}
        commandDescription={commandDescription}
      />
    );
  },
};
