import { cn } from '@repo/ui/lib/utils';
import { CheckCircle2, Circle, Loader2 } from 'lucide-react';

interface ProgressStep {
  label: string;
  status: 'pending' | 'processing' | 'completed';
}

interface ProgressIndicatorProps {
  steps: ProgressStep[];
  currentStep?: number;
  className?: string;
}

export const ProgressIndicator = ({
  steps,
  currentStep = 0,
  className,
}: ProgressIndicatorProps) => {
  return (
    <div className={cn('flex flex-col gap-2', className)}>
      {steps.map((step, index) => {
        const isActive = index === currentStep;
        const isCompleted = step.status === 'completed';
        const isProcessing = step.status === 'processing';

        return (
          <div
            key={index}
            className={cn(
              'flex items-center gap-2 text-sm',
              isActive && 'text-primary',
              isCompleted && 'text-success',
              !isActive && !isCompleted && 'text-muted-foreground',
            )}
          >
            {isProcessing ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : isCompleted ? (
              <CheckCircle2 className="h-4 w-4" />
            ) : (
              <Circle className="h-4 w-4" />
            )}
            <span>{step.label}</span>
          </div>
        );
      })}
    </div>
  );
};
