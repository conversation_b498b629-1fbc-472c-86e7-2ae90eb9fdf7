import { TOOL_TYPES } from '@repo/common/consts/tool/const';
import { BoardItemTypeEnum } from '@repo/common/types/board-item/types';
import { MessageStatusEnum } from '@repo/common/types/chat/enum';
import { useAtom } from 'jotai';
import { Play } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { boardDetailAtom, refreshBoardDetailAtom } from '@/hooks/useBoards';
import { CompletionBlockStatusEnum, CompletionToolBlock } from '@/typings/completion-block';
import { ToolStatus, ToolTitle } from '../card';
import { useAddBoardItemsWhenBlockRunning } from '../hooks/useAddSnipResultToBoard';
import { useMessage } from '../hooks/useMessage';
import type { TOOL_RENDERER } from '../type';
import { ProgressIndicator } from './ProgressIndicator';
import { ResultCard } from './ResultCard';

const titles: Record<CompletionBlockStatusEnum, string> = {
  [CompletionBlockStatusEnum.generating]: 'Forging YouTube Intelligence…',
  [CompletionBlockStatusEnum.executing]: 'Forging YouTube Intelligence…',
  [CompletionBlockStatusEnum.success]: 'YouTube Intelligence Forged',
  [CompletionBlockStatusEnum.errored]: 'Failed to process YouTube video',
  [CompletionBlockStatusEnum.unknownDefaultOpenApi]: 'Failed to process YouTube video',
};

export const YoutubeFactoryCard = ({
  block,
  variant,
}: {
  block: CompletionToolBlock;
  variant: 'small' | 'middle';
}) => {
  const [, refreshBoardDetail] = useAtom(refreshBoardDetailAtom);
  const message = useMessage();
  const [boardDetail] = useAtom(boardDetailAtom);
  const [isExpanded, setIsExpanded] = useState(true);

  // Extract progress data from tool_result
  const progress = block.tool_result?.progress;
  const boardGroups = block.tool_result?.board_groups;
  const snipInfo = block.tool_result?.snip_info;
  const thoughtResult = block.tool_result?.thought_result;
  const quizResult = block.tool_result?.quiz_result;
  const relatedSnips = block.tool_result?.related_snips || [];
  const relatedProgress = block.tool_result?.related_progress;
  const goldenQuotesResult = block.tool_result?.golden_quotes_result;

  // Generate progress steps
  const progressSteps = useMemo(() => {
    const steps = [
      {
        label: 'Analyzing video information',
        status: snipInfo ? 'completed' : progress?.progress >= 1 ? 'processing' : 'pending',
      },
      {
        label: 'Creating board groups',
        status: boardGroups ? 'completed' : progress?.progress >= 2 ? 'processing' : 'pending',
      },
      {
        label: 'Creating thought from transcript',
        status: thoughtResult ? 'completed' : progress?.progress >= 3 ? 'processing' : 'pending',
      },
      {
        label: 'Creating quiz from video content',
        status: quizResult ? 'completed' : progress?.progress >= 4 ? 'processing' : 'pending',
      },
      {
        label: `Creating related videos${relatedProgress ? ` (${relatedProgress.processing_index}/${relatedProgress.total_videos})` : ''}`,
        status:
          // 如果有相关视频并且都成功创建了，或者状态已完成，则显示完成
          (relatedSnips.length > 0 && relatedSnips.every((r: { success: boolean }) => r.success)) ||
          (progress?.status === 'completed' && progress?.progress >= 5)
            ? 'completed'
            : progress?.progress >= 5
              ? 'processing'
              : 'pending',
      },
      {
        label: 'Creating golden quote cards',
        status: goldenQuotesResult
          ? 'completed'
          : progress?.progress >= 6
            ? 'processing'
            : 'pending',
      },
    ];
    return steps.map((step) => ({
      ...step,
      status: step.status as 'pending' | 'processing' | 'completed',
    }));
  }, [
    progress,
    boardGroups,
    snipInfo,
    thoughtResult,
    quizResult,
    relatedProgress,
    relatedSnips,
    goldenQuotesResult,
  ]);

  // Calculate current step for progress indicator
  const currentStep = useMemo(() => {
    if (progress?.status === 'completed') return -1;
    return Math.max(0, (progress?.progress || 0) - 1);
  }, [progress]);

  // Refresh board when board groups are created
  useEffect(() => {
    if (boardGroups?.refresh_board && boardGroups?.groups?.length > 0) {
      refreshBoardDetail();
    }
  }, [boardGroups, refreshBoardDetail]);

  // Refresh board when processing completes
  useEffect(() => {
    if (message?.status !== MessageStatusEnum.ING) {
      return;
    }
    if (block.status !== CompletionBlockStatusEnum.success) return;
    if (thoughtResult?.board_id === boardDetail?.id || quizResult?.board_id === boardDetail?.id) {
      refreshBoardDetail();
    }
  }, [
    block.status,
    message?.status,
    thoughtResult,
    quizResult,
    boardDetail?.id,
    refreshBoardDetail,
  ]);

  // Add board items when block is running
  // @ts-expect-error
  useAddBoardItemsWhenBlockRunning(block, (_block) => {
    const items = [];

    // Add thought to board
    if (thoughtResult?.thought) {
      items.push({
        id: thoughtResult.thought.boardItem?.id || thoughtResult.thought_id,
        created_at: thoughtResult.thought?.createdAt || new Date(),
        updated_at: thoughtResult.thought?.updatedAt || new Date(),
        board_id:
          thoughtResult.thought.boardItem?.boardId || thoughtResult.board_id || boardDetail!.id,
        thought_id: thoughtResult.thought_id,
        snip_id: undefined,
        chat_id: undefined,
        board_group_id: thoughtResult.thought.boardItem?.parentBoardGroupId,
        parent_board_group_id: thoughtResult.thought.boardItem?.parentBoardGroupId,
        rank: thoughtResult.thought.boardItem?.rank || '',
        entity_type: BoardItemTypeEnum.THOUGHT,
        entity: thoughtResult.thought, // 使用完整的 thought 实体
      });
    }

    // Add quiz to board
    if (quizResult?.snip) {
      items.push({
        id: quizResult.snip.boardItem?.id || quizResult.quiz_id,
        created_at: quizResult.snip.created_at || new Date(),
        updated_at: quizResult.snip.updated_at || new Date(),
        board_id: quizResult.snip.boardItem?.boardId || quizResult.board_id || boardDetail!.id,
        snip_id: quizResult.quiz_id,
        thought_id: undefined,
        chat_id: undefined,
        board_group_id: quizResult.snip.boardItem?.parentBoardGroupId,
        parent_board_group_id: quizResult.snip.boardItem?.parentBoardGroupId,
        rank: quizResult.snip.boardItem?.rank || '',
        entity_type: BoardItemTypeEnum.SNIP,
        entity: quizResult.snip, // 使用完整的 snip 实体
      });
    }

    // Add related snips to board
    relatedSnips.forEach(
      (related: {
        success: boolean;
        snip?: {
          id: string;
          createdAt: Date;
          updatedAt: Date;
          boardItem?: {
            id: string;
            board_id: string;
            rank: string;
            parentBoardGroupId: string;
          };
        };
        snip_id?: string;
      }) => {
        if (related.success && related.snip) {
          items.push({
            id: related.snip.boardItem?.id || related.snip_id,
            created_at: related.snip.createdAt || new Date(),
            updated_at: related.snip.updatedAt || new Date(),
            board_id: related.snip.boardItem?.board_id || boardDetail!.id,
            snip_id: related.snip_id,
            thought_id: undefined,
            chat_id: undefined,
            board_group_id: related.snip.boardItem?.parentBoardGroupId,
            parent_board_group_id: related.snip.boardItem?.parentBoardGroupId,
            rank: related.snip.boardItem?.rank || '',
            entity_type: BoardItemTypeEnum.SNIP,
            entity: related.snip, // 使用完整的 snip 实体
          });
        }
      },
    );

    // Add golden quote cards to board
    if (goldenQuotesResult?.golden_quote_thoughts) {
      goldenQuotesResult.golden_quote_thoughts.forEach(
        (goldenThought: {
          id: string;
          createdAt: Date;
          updatedAt: Date;
          boardItem?: {
            id: string;
            boardId: string;
            rank: string;
            parentBoardGroupId: string;
          };
        }) => {
          items.push({
            id: goldenThought.boardItem?.id || goldenThought.id,
            created_at: goldenThought.createdAt || new Date(),
            updated_at: goldenThought.updatedAt || new Date(),
            board_id: goldenThought.boardItem?.boardId || boardDetail!.id,
            thought_id: goldenThought.id,
            snip_id: undefined,
            chat_id: undefined,
            board_group_id: goldenThought.boardItem?.parentBoardGroupId,
            parent_board_group_id: goldenThought.boardItem?.parentBoardGroupId,
            rank: goldenThought.boardItem?.rank || '',
            entity_type: BoardItemTypeEnum.THOUGHT,
            entity: goldenThought, // 使用完整的 thought 实体
          });
        },
      );
    }

    return items;
  });

  const renderContent = () => {
    if (!isExpanded) {
      return null;
    }

    // 判断是否显示进度指示器（执行中或完成后都显示）
    const showProgress =
      block.status === CompletionBlockStatusEnum.executing ||
      (block.status === CompletionBlockStatusEnum.success &&
        progressSteps.some((s) => s.status === 'completed'));

    return (
      <div className="mt-3 space-y-4 rounded-[16px] border border-muted bg-card p-4">
        {/* Progress Indicator - 保留在成功状态也显示 */}
        {showProgress && (
          <div className="mb-4">
            <ProgressIndicator steps={progressSteps} currentStep={currentStep} />
          </div>
        )}

        {/* Results Section - 按照新的执行顺序显示 */}
        <div className="space-y-3">
          {/* Step 1: Video Info - 显示获取到的视频信息 */}
          {snipInfo && (
            <ResultCard
              type="video-info"
              title={snipInfo.title}
              subtitle={
                snipInfo.has_transcript ? '✓ Transcript available' : 'No transcript available'
              }
              status="completed"
            />
          )}

          {/* Step 2: Board Groups Result */}
          {boardGroups && (
            <ResultCard
              type="board-groups"
              title={`${boardGroups.created_count}/${boardGroups.total_count} groups created`}
              subtitle={
                boardGroups.groups?.length > 0
                  ? boardGroups.groups
                      .map((group: { id: string; name: string }) => group.name)
                      .join(', ')
                  : undefined
              }
              status="completed"
            />
          )}

          {/* Step 3: Thought Result */}
          {thoughtResult && (
            <ResultCard
              type="thought"
              title={thoughtResult.title}
              subtitle={`${thoughtResult.content_length} characters`}
              status="completed"
            />
          )}

          {/* Step 4: Quiz Result */}
          {quizResult && (
            <ResultCard
              type="quiz"
              title={quizResult.title}
              status="completed"
              subtitle={`10 questions`}
            />
          )}

          {/* Step 5: Related Videos */}

          {relatedSnips.map(
            (related: { title?: string; video_id: string; success: boolean }, index: number) => (
              <ResultCard
                key={index}
                type="related"
                title={related.title || `Video ${index + 1}`}
                subtitle={related.video_id}
                status={related.success ? 'completed' : 'error'}
              />
            ),
          )}

          {/* Step 6: Golden Quote Cards */}
          {goldenQuotesResult && (
            <ResultCard
              type="golden-quotes"
              title={`${goldenQuotesResult.created_count}/${goldenQuotesResult.total_count} golden quote cards created`}
              subtitle="Golden quotes extracted from transcript"
              status="completed"
            />
          )}
        </div>
      </div>
    );
  };

  return (
    <div>
      <ToolTitle
        text={renderTitle(block)}
        icon={<Play size={15} />}
        variant={variant}
        blockStatus={block.status}
        onClick={() => setIsExpanded(!isExpanded)}
      />
      {renderContent()}
    </div>
  );
};

const renderTitle = (block: CompletionToolBlock) => {
  const progress = block.tool_result?.progress;
  const result = block.tool_result?.result;
  const thoughtTitle = result?.title || block.tool_result?.thought_result?.title;

  if (block.status === CompletionBlockStatusEnum.executing && progress?.step) {
    return (
      <>
        <span>{progress.step}</span>
        {progress.progress && progress.total && (
          <span className="ml-2 text-xs text-caption-fg">
            Step {progress.progress} of {progress.total}
          </span>
        )}
      </>
    );
  }

  return (
    <>
      <span>{titles[block.status]}</span>
      {thoughtTitle && (
        <span className="ml-2 text-xs text-caption-fg">Created: {thoughtTitle}</span>
      )}
    </>
  );
};

export const YoutubeFactoryTool: TOOL_RENDERER = {
  type: TOOL_TYPES.YOUTUBE_FACTORY,
  renderer: YoutubeFactoryCard,
  getToolTitle: (block) => {
    const result = block.tool_result?.result;
    const progress = block.tool_result?.progress;
    const thoughtTitle = result?.title || block.tool_result?.thought_result?.title;

    let description: string | undefined;
    if (block.status === CompletionBlockStatusEnum.executing && progress?.step) {
      description = `${progress.step} (${progress.progress}/${progress.total})`;
    } else if (thoughtTitle) {
      description = `Created: ${thoughtTitle}`;
    }

    return (
      <ToolStatus
        logo={<Play size={17} />}
        commandName={titles[block.status]}
        commandDescription={description}
      />
    );
  },
  transformToMarkdown: (block) => {
    const result = block.tool_result?.result;
    const summaryMessage = block.tool_result?.summary_message;

    if (summaryMessage) {
      return summaryMessage;
    }

    const thoughtTitle = result?.title || block.tool_result?.thought_result?.title;
    const contentLength =
      result?.content_length || block.tool_result?.thought_result?.content_length;
    const relatedCount = block.tool_result?.related_snips?.length || 0;
    const goldenQuotesCount = block.tool_result?.golden_quotes_result?.created_count || 0;

    if (thoughtTitle) {
      return `YouTube Intelligence Forged: Created thought "${thoughtTitle}" with ${contentLength || 'unknown'} characters, quiz, ${relatedCount} related videos, and ${goldenQuotesCount} golden quote cards.`;
    }
    return 'YouTube Intelligence Forged.';
  },
  needRefreshBoard: true,
};
