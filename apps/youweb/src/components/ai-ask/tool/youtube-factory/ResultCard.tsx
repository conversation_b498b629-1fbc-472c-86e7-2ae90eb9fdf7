import { cn } from '@repo/ui/lib/utils';
import { FileText, FolderTree, PlayCircle, Quote, Sparkles, Video } from 'lucide-react';

interface ResultCardProps {
  type: 'thought' | 'quiz' | 'related' | 'board-groups' | 'video-info' | 'golden-quotes';
  title?: string;
  subtitle?: string;
  status?: 'pending' | 'processing' | 'completed' | 'error';
  className?: string;
  onClick?: () => void;
}

export const ResultCard = ({
  type,
  title,
  subtitle,
  status = 'pending',
  className,
  onClick,
}: ResultCardProps) => {
  const getIcon = () => {
    switch (type) {
      case 'thought':
        return <FileText className="h-4 w-4" />;
      case 'quiz':
        return <Sparkles className="h-4 w-4" />;
      case 'related':
        return <Video className="h-4 w-4" />;
      case 'board-groups':
        return <FolderTree className="h-4 w-4" />;
      case 'video-info':
        return <PlayCircle className="h-4 w-4" />;
      case 'golden-quotes':
        return <Quote className="h-4 w-4" />;
      default:
        return <PlayCircle className="h-4 w-4" />;
    }
  };

  const getTypeLabel = () => {
    switch (type) {
      case 'thought':
        return 'Transcript';
      case 'quiz':
        return 'Quiz';
      case 'related':
        return 'Related Video';
      case 'board-groups':
        return 'Board Groups';
      case 'video-info':
        return 'Video Information';
      case 'golden-quotes':
        return 'Golden Quote Cards';
      default:
        return 'Item';
    }
  };

  return (
    <div
      className={cn(
        'flex items-start gap-3 rounded-[12px] border border-muted p-3 transition-colors shadow-sm',
        status === 'completed' && 'border-muted',
        status === 'processing' && 'animate-pulse',
        status === 'error' && 'border-muted',
        onClick && 'cursor-pointer hover:bg-accent',
        className,
      )}
      onClick={onClick}
    >
      <div
        className={cn(
          'flex h-8 w-8 items-center justify-center rounded-full',
          status === 'completed' && '',
          status === 'processing' && '',
          status === 'error' && '',
          status === 'pending' && '',
        )}
      >
        {getIcon()}
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2">
          <span className="text-xs font-medium text-muted-foreground">{getTypeLabel()}</span>
          {status === 'processing' && <span className="text-xs text-primary">Creating...</span>}
        </div>
        {title && <p className="mt-0.5 text-sm font-medium line-clamp-1">{title}</p>}
        {subtitle && (
          <p className="mt-0.5 text-xs text-muted-foreground line-clamp-1">{subtitle}</p>
        )}
      </div>
    </div>
  );
};
