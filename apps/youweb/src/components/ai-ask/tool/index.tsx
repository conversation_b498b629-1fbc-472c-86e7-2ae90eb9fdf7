import { AssistantMessage } from '@/hooks/ask-ai/types';
import { CompletionToolBlock } from '@/typings/completion-block';
import { GenerateAudioTool } from './audio-generate';
import { CreateBoardTool } from './create_board';
import { CreateSnipByUrlTool } from './create-snip-by-url/create-snip-by-url';
import { GenerateDiagramTool } from './diagram-generate';
import { EditThoughtTool } from './edit_thought';
import { FakeCard } from './fake';
import { SearchTool } from './google-search';
import { GenerateImageTool } from './image-generate';
import { BoardSearchTool, LibrarySearchTool } from './library-search';
import { OrganizeDirectoryStructureTool } from './organize_directory_structure';
import { PrefetchUrlsTool } from './prefetch-urls';
import { TOOL_RENDERER, TOOL_SCENE_TYPE } from './type';
import { YoutubeFactoryTool } from './youtube-factory/youtube-factory';

export const toolDefs: TOOL_RENDERER[] = [
  SearchTool,
  GenerateDiagramTool,
  GenerateImageTool,
  LibrarySearchTool,
  BoardSearchTool,
  CreateSnipByUrlTool,
  CreateBoardTool,
  EditThoughtTool,
  OrganizeDirectoryStructureTool,
  GenerateAudioTool,
  YoutubeFactoryTool,
  PrefetchUrlsTool,
];

export const ToolBlock = ({
  block,
  message,
  variant = 'small',
  scene,
}: {
  block: CompletionToolBlock;
  message: AssistantMessage;
  variant: 'small' | 'middle';
  scene: TOOL_SCENE_TYPE;
}) => {
  const { tool_name } = block;

  const toolDef = toolDefs.find((tool) => tool.type === tool_name);
  if (!toolDef) {
    return <FakeCard block={block} variant={variant} />;
  }
  return <toolDef.renderer block={block} variant={variant} message={message} scene={scene} />;
};
