import { zodResolver } from '@hookform/resolvers/zod';
import { FileInput, FileUploader } from '@repo/ui/components/custom/file-upload';
import { PreviewInvoiceDialog } from '@repo/ui/components/custom/subscription/preview-invoice-dialog';
import { SubscriptionBadge } from '@repo/ui/components/custom/subscription-badge';
import { Avatar, AvatarFallback, AvatarImage } from '@repo/ui/components/ui/avatar';
import { Button } from '@repo/ui/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@repo/ui/components/ui/dialog';
import { Form, FormControl, FormField, FormItem } from '@repo/ui/components/ui/form';
import { Input } from '@repo/ui/components/ui/input';
import { Label } from '@repo/ui/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/ui/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@repo/ui/components/ui/table';
import { useAsyncEffect } from 'ahooks';
import { format } from 'date-fns';
import { useAtom } from 'jotai';
import { Calendar, ChevronLeft, ChevronRight, Copy, Edit2, ExternalLink } from 'lucide-react';
import React, { useState } from 'react';
import { type SubmitHandler, useForm } from 'react-hook-form';
import { z } from 'zod';
import { CreditsUsage } from '@/components/credits-usage';
import { EmployeeOnly } from '@/components/EmployeeOnly';
import { DateTimePicker } from '@/components/ui/custom/date-time-picker';
import { Separator } from '@/components/ui/separator';
import {
  creditAccountAtom,
  isCurrentPlan,
  subscriptionAtom,
  useCreditAccount,
} from '@/hooks/useSubscription';
import { useTranslation } from '@/hooks/useTranslation';
import { uploadFile } from '@/hooks/useUploadFiles';
import { userAtom } from '@/hooks/useUser';
import {
  BillingInterval,
  CreditTransaction,
  Subscription,
  SubscriptionProductTier,
} from '@/typings/subscription';
import { User } from '@/typings/user';
import { apiClient, callAPI } from '@/utils/callHTTP';
import { cn, sha256File } from '@/utils/utils';

const DEFAULT_AVATAR = 'https://cdn.gooo.ai/assets/default-user-picture.png';

export interface AccountProps extends React.HTMLAttributes<HTMLDivElement> {}

const FormSchema = z.object({
  name: z.string(),
});
type FormType = z.infer<typeof FormSchema>;

const MAX_FILES = 1;
const MAX_FILE_SIZE = 1024 * 1024 * 10;
const dropZoneConfig = {
  maxFiles: MAX_FILES,
  maxSize: MAX_FILE_SIZE,
  multiple: false,
  accept: {
    'image/*': ['.png', '.gif', '.jpeg', '.jpg'],
  },
};

export default function Account({ className }: AccountProps) {
  const { t } = useTranslation('Settings');
  const [user, setUser] = useAtom(userAtom);

  const [files, setFiles] = useState<File[] | null>(null);
  const [open, setOpen] = useState(false);
  const [loadingActions, setLoadingActions] = useState<Record<string, boolean>>({});

  // Dialog states
  const [timeZoneDialogOpen, setTimeZoneDialogOpen] = useState(false);
  const [testClockDialogOpen, setTestClockDialogOpen] = useState(false);
  const [consumeCreditsDialogOpen, setConsumeCreditsDialogOpen] = useState(false);
  const [rechargeCreditsDialogOpen, setRechargeCreditsDialogOpen] = useState(false);

  // Form values
  const [timeZoneValue, setTimeZoneValue] = useState('');
  const [testClockDateTime, setTestClockDateTime] = useState<Date>();
  const [consumeAmount, setConsumeAmount] = useState('');
  const [rechargeAmount, setRechargeAmount] = useState('');

  // Transaction detail dialog
  const [selectedTransaction, setSelectedTransaction] = useState<CreditTransaction | null>(null);
  const [transactionDetailDialogOpen, setTransactionDetailDialogOpen] = useState(false);

  // Preview invoice dialog
  const [previewInvoiceData, setPreviewInvoiceData] = useState<any>(null);
  const [previewInvoiceDialogOpen, setPreviewInvoiceDialogOpen] = useState(false);
  const [pendingSubscriptionUpdate, setPendingSubscriptionUpdate] = useState<{
    product_tier: SubscriptionProductTier;
    billing_interval: BillingInterval;
  } | null>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  const picture = user?.picture || DEFAULT_AVATAR;

  const userName = user?.name || user?.email?.split('@')[0] || '';
  let avatar: string;
  if (picture.startsWith('files/')) {
    avatar = `/${picture}`;
  } else {
    avatar = picture;
  }

  const form = useForm<FormType>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      name: userName,
    },
  });

  const [creditTransactions, setCreditTransactions] = useState<CreditTransaction[]>([]);

  // Calculate pagination
  const totalItems = creditTransactions.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedTransactions = creditTransactions.slice(startIndex, endIndex);

  // Reset to page 1 when transactions change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [creditTransactions]);

  const [creditAccount, setCreditAccount] = useAtom(creditAccountAtom);
  const [subscription, setSubscription] = useAtom(subscriptionAtom);
  const { setSubscriptionDialogOpen } = useCreditAccount();

  const isAppleSubscription = subscription?.provider === 'apple';

  useAsyncEffect(async () => {
    const { data } = await callAPI(
      apiClient.creditApi.listCreditsConsumeTransactionsInCurrentPeriod(),
    );
    if (data) {
      setCreditTransactions(data.transactions);
    }
  }, [user]);

  // Initialize testClockDateTime when dialog opens
  React.useEffect(() => {
    if (testClockDialogOpen && !testClockDateTime) {
      const defaultDateTime = creditAccount?.test_clock
        ? // @ts-ignore
          new Date(creditAccount.test_clock.frozen_time * 1000)
        : new Date();
      setTestClockDateTime(defaultDateTime);
    }
  }, [testClockDialogOpen, testClockDateTime, creditAccount?.test_clock]);

  const handleCreateSubscription = async (
    product_tier: SubscriptionProductTier,
    billing_interval: BillingInterval,
  ) => {
    const { data } = await callAPI(
      apiClient.subscriptionApi.createSubscription({
        product_tier,
        billing_interval,
      }),
    );
    if (data) {
      if (data.redirect) {
        window.location.href = data.redirect;
      }
      if (data.subscription) {
        setSubscription(data.subscription);
      }
      if (data.credit_account) {
        setCreditAccount(data.credit_account);
      }
    }
  };

  const handleCancelSubscription = async () => {
    const { data } = await callAPI(apiClient.subscriptionApi.cancelSubscription({}));
    if (data) {
      setSubscription(data);
    }
  };

  const handleCreateBillingPortalSession = async () => {
    setLoadingActions((prev) => ({ ...prev, 'create-billing-portal-session': true }));
    try {
      const { data } = await callAPI(apiClient.subscriptionApi.createBillingPortalSession());
      if (data) {
        window.location.href = data.redirect;
      }
    } finally {
      setLoadingActions((prev) => ({ ...prev, 'create-billing-portal-session': false }));
    }
  };

  const handleUpdateSubscription = async (
    product_tier: SubscriptionProductTier,
    billing_interval: BillingInterval,
  ) => {
    const { data } = await callAPI(
      apiClient.subscriptionApi.updateSubscription({
        product_tier,
        billing_interval,
        preview_if_upgrade: true, // 升级时先预览
      }),
    );
    if (data) {
      // 如果返回预览账单，显示确认弹框
      if (data.preview_invoice) {
        setPreviewInvoiceData(data.preview_invoice);
        setPendingSubscriptionUpdate({ product_tier, billing_interval });
        setPreviewInvoiceDialogOpen(true);
        return;
      }

      // 正常处理订阅更新
      if (data.subscription) {
        setSubscription(data.subscription);
      }
      if (data.credit_account) {
        setCreditAccount(data.credit_account);
      }
      if (data.redirect) {
        window.location.href = data.redirect;
      }
    }
  };

  const handleDeleteSubscriptionForTest = async () => {
    await callAPI(apiClient.subscriptionApi.deleteSubscriptionForTest());
    // 删除后清空订阅状态
    setSubscription(null);
  };

  const handleConfirmPreviewInvoice = async () => {
    if (!pendingSubscriptionUpdate) return;

    // 调用实际的订阅更新 API，不预览直接执行
    const { data } = await callAPI(
      apiClient.subscriptionApi.updateSubscription({
        product_tier: pendingSubscriptionUpdate.product_tier,
        billing_interval: pendingSubscriptionUpdate.billing_interval,
        preview_if_upgrade: false, // 不预览，直接执行
      }),
    );

    if (data) {
      // 处理更新结果
      if (data.subscription) {
        setSubscription(data.subscription);
      }
      if (data.credit_account) {
        setCreditAccount(data.credit_account);
      }
      if (data.redirect) {
        window.location.href = data.redirect;
      }

      // 成功后清空状态
      setPreviewInvoiceData(null);
      setPendingSubscriptionUpdate(null);
    }
  };

  // 辅助函数：获取按钮文案
  const getButtonText = (
    subscription: Subscription,
    targetTier: SubscriptionProductTier,
    targetInterval: BillingInterval,
  ) => {
    const tierName = targetTier === 'pro' ? 'Pro' : 'Max';
    const intervalName = targetInterval === 'monthly' ? 'monthly' : 'yearly';

    // 如果不是当前计划，显示 "Change to"
    if (!isCurrentPlan(subscription, targetTier, targetInterval)) {
      return `Change to ${tierName} ${intervalName}`;
    }

    // 如果是当前计划，显示计划名称
    let text = `${tierName} ${intervalName}`;

    // 如果是 renew_change 计划，加上尾巴
    if (subscription.renew_change) {
      text += ' ✓ (at period end)';
    }

    return text;
  };

  // 辅助函数：获取所有可用的订阅选项
  const getAvailablePlans = () => {
    // 所有用户都可以选择所有计划
    return [
      { tier: SubscriptionProductTier.pro, interval: BillingInterval.monthly },
      { tier: SubscriptionProductTier.pro, interval: BillingInterval.yearly },
      { tier: SubscriptionProductTier.max, interval: BillingInterval.monthly },
      { tier: SubscriptionProductTier.max, interval: BillingInterval.yearly },
    ];
  };

  const handleUpdateTimeZone = async (timeZone: string) => {
    const { data } = await callAPI(
      apiClient.userApi.updateTimeZone({
        time_zone: timeZone,
      }),
    );

    // 更新用户状态
    if (data) {
      setUser({
        ...user,
        ...data,
      } as User);
    }

    setTimeZoneDialogOpen(false);
  };

  const handleAdvanceTestClock = async (fozenTime: Date) => {
    const { data } = await callAPI(
      apiClient.creditApi.advanceTestClock({
        fozen_time: fozenTime,
      }),
    );
    if (data) {
      setCreditAccount(data);
    }

    setTestClockDialogOpen(false);
  };

  const handleConsumeCredits = async (amount: number) => {
    await callAPI(
      apiClient.creditApi.consumeCredits({
        amount,
      }),
    );
    setConsumeCreditsDialogOpen(false);
    // 刷新 CreditAccount
    const { data: creditData } = await callAPI(apiClient.creditApi.getCreditAccount());
    if (creditData) {
      setCreditAccount(creditData);
    }
  };

  const handleRechargeCredits = async (amount: number) => {
    await callAPI(
      apiClient.creditApi.rechargeCredits({
        amount,
        reason: 'Manual recharge',
      }),
    );
    setRechargeCreditsDialogOpen(false);
    // 刷新 CreditAccount
    const { data: creditData } = await callAPI(apiClient.creditApi.getCreditAccount());
    if (creditData) {
      setCreditAccount(creditData);
    }
  };

  const handleAsyncAction = async (actionKey: string, action: () => Promise<void>) => {
    setLoadingActions((prev) => ({ ...prev, [actionKey]: true }));
    try {
      await action();
    } finally {
      setLoadingActions((prev) => ({ ...prev, [actionKey]: false }));
    }
  };

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // TODO: 添加成功提示 toast
      console.log(`${label} ID copied to clipboard: ${text}`);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  const pending = form.formState.isSubmitting;

  const onSubmit: SubmitHandler<FormType> = async (userData) => {
    const { data, error } = await callAPI(
      apiClient.userApi.updateUserName({
        name: userData.name,
      }),
    );

    if (error) {
      return;
    }

    if (data) {
      setUser({
        ...user,
        ...data,
      } as User);
    }
    setOpen(false);
  };

  const handleFileChange = async (files: File[] | null) => {
    if (files?.length) {
      setFiles(files);
      const file = files[0];
      const hash = await sha256File(file);
      try {
        await uploadFile({ hash, file, isPublic: true });
      } catch {
        setFiles([]);
        return;
      }

      const { data, error } = await callAPI(
        apiClient.userApi.updateUserAvatar({
          avatar_url: `https://cdn.gooo.ai/web-images/${hash}`,
        }),
      );

      if (error) {
        setFiles([]);
        return;
      }

      if (data) {
        setUser({
          ...user,
          ...data,
        } as User);
      }
    }
  };

  return (
    <div className={cn('rounded-2xl bg-background p-2', className)}>
      <div className="flex justify-between">
        <div className="px-4 pb-4 pt-2 w-full">
          <p className="title mb-4">{t('Account.title')}</p>
          <div className="flex justify-between">
            <div className="flex gap-4 flex-1">
              <FileUploader
                value={files}
                onValueChange={handleFileChange}
                dropzoneOptions={dropZoneConfig}
                className="w-auto"
              >
                <FileInput>
                  <Avatar className="h-[52px] w-[52px] object-cover">
                    <AvatarImage src={avatar} alt="user avatar" className="object-cover" />
                    <AvatarFallback></AvatarFallback>
                  </Avatar>
                </FileInput>
              </FileUploader>
              <div className="flex-1">
                <p className="body-strong mb-2 flex items-center">
                  {userName}
                  <Dialog open={open} onOpenChange={setOpen}>
                    <DialogTrigger asChild>
                      <Button variant="icon" size="sm" className="ml-1 text-caption-fg">
                        <Edit2 />
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="w-[400px]">
                      <DialogHeader>
                        <DialogTitle>{t('Account.changeName')}</DialogTitle>
                      </DialogHeader>
                      <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)}>
                          <FormField
                            control={form.control}
                            name="name"
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Input className="h-8 rounded-md" placeholder="" {...field} />
                                </FormControl>
                              </FormItem>
                            )}
                          />
                          <DialogFooter>
                            <Button
                              type="submit"
                              className="mt-4 h-8 rounded-full bg-foreground text-background hover:bg-foreground hover:text-background"
                              disabled={pending}
                            >
                              {t('Account.apply')}
                            </Button>
                          </DialogFooter>
                        </form>
                      </Form>
                    </DialogContent>
                  </Dialog>
                  <SubscriptionBadge className="ml-2" productTier={creditAccount?.product_tier} />
                </p>
                <div className="flex items-center footnote text-caption-fg gap-2">
                  <p>Email: {user?.email}</p>
                  <Separator orientation="vertical" className="h-2" />
                  <p className="text-disabled-fg flex-1 text-ellipsis overflow-hidden whitespace-nowrap">
                    UID: {user?.id}
                  </p>
                </div>
              </div>
            </div>

            {!isAppleSubscription && (
              <div className="flex items-center gap-3">
                {subscription && (
                  <Button
                    variant="outline"
                    loading={loadingActions['create-billing-portal-session']}
                    onClick={handleCreateBillingPortalSession}
                  >
                    Manage
                  </Button>
                )}
                <Button onClick={() => setSubscriptionDialogOpen(true)}>Upgrade</Button>
              </div>
            )}
          </div>
        </div>
      </div>

      {isAppleSubscription && (
        <p className="bg-snip-card text-secondary-fg footnote px-3 py-[6px] rounded-xl mb-6 mx-4">
          You purchased subscription on iOS. Use the YouMind app to{' '}
          <a
            href="https://support.apple.com/en-us/118428"
            target="_blank"
            rel="noopener noreferrer"
            className="text-function-link"
          >
            manage it in Apple.
          </a>
        </p>
      )}
      {creditAccount && (
        <CreditsUsage
          credits={creditAccount.monthly_balance}
          usage={creditAccount.monthly_quota}
          daysLeft={Math.ceil(
            (creditAccount.current_period_end.getTime() - Date.now()) / (1000 * 60 * 60 * 24),
          )}
        />
      )}

      <EmployeeOnly alwaysShowInPreview>
        <div className="mt-4 space-y-3">
          {/* User Card */}
          <div className="rounded-lg border p-4 bg-card">
            <h3 className="text-lg font-semibold mb-3">User</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2 flex-1">
                  <p className="text-sm text-muted-foreground">Time zone:</p>
                  <p className="font-medium text-sm">{user?.time_zone || 'Not set'}</p>
                </div>
                <Dialog open={timeZoneDialogOpen} onOpenChange={setTimeZoneDialogOpen}>
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        // 初始化当前时区值
                        const currentTimeZone =
                          user?.time_zone || Intl.DateTimeFormat().resolvedOptions().timeZone;
                        setTimeZoneValue(currentTimeZone);
                        setTimeZoneDialogOpen(true);
                      }}
                    >
                      Change
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="w-[400px]">
                    <DialogHeader>
                      <DialogTitle>Update time zone</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div>
                        <Label>Time zone</Label>
                        <Select value={timeZoneValue} onValueChange={setTimeZoneValue}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a time zone" />
                          </SelectTrigger>
                          <SelectContent>
                            {/* 如果当前时区不在预定义列表中，先显示当前时区 */}
                            {timeZoneValue &&
                              ![
                                'UTC',
                                'Asia/Shanghai',
                                'Asia/Tokyo',
                                'Asia/Kolkata',
                                'Europe/London',
                                'Europe/Paris',
                                'America/New_York',
                                'America/Chicago',
                                'America/Denver',
                                'America/Los_Angeles',
                                'Australia/Sydney',
                              ].includes(timeZoneValue) && (
                                <>
                                  <SelectItem value={timeZoneValue}>
                                    {timeZoneValue} (Current)
                                  </SelectItem>
                                  <div className="h-px bg-muted mx-1 my-1" />
                                </>
                              )}
                            <SelectItem value="UTC">UTC (UTC+0)</SelectItem>
                            <SelectItem value="Asia/Shanghai">Asia/Shanghai (UTC+8)</SelectItem>
                            <SelectItem value="Asia/Tokyo">Asia/Tokyo (UTC+9)</SelectItem>
                            <SelectItem value="Asia/Kolkata">Asia/Kolkata (UTC+5:30)</SelectItem>
                            <SelectItem value="Europe/London">Europe/London (UTC+0/+1)</SelectItem>
                            <SelectItem value="Europe/Paris">Europe/Paris (UTC+1/+2)</SelectItem>
                            <SelectItem value="America/New_York">
                              America/New_York (UTC-5/-4)
                            </SelectItem>
                            <SelectItem value="America/Chicago">
                              America/Chicago (UTC-6/-5)
                            </SelectItem>
                            <SelectItem value="America/Denver">
                              America/Denver (UTC-7/-6)
                            </SelectItem>
                            <SelectItem value="America/Los_Angeles">
                              America/Los_Angeles (UTC-8/-7)
                            </SelectItem>
                            <SelectItem value="Australia/Sydney">
                              Australia/Sydney (UTC+10/+11)
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setTimeZoneDialogOpen(false)}>
                        Cancel
                      </Button>
                      <Button
                        loading={loadingActions['set-timezone']}
                        onClick={() =>
                          handleAsyncAction('set-timezone', () =>
                            handleUpdateTimeZone(timeZoneValue),
                          )
                        }
                        disabled={!timeZoneValue.trim()}
                      >
                        Update
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>

              {creditAccount?.test_clock && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 flex-1">
                    <p className="text-sm text-muted-foreground">Test clock:</p>
                    <p className="font-medium text-orange-600 text-sm">
                      {format(
                        // @ts-ignore
                        new Date(creditAccount.test_clock.frozen_time * 1000),
                        'yyyy-MM-dd HH:mm:ss',
                      )}
                    </p>
                    <div
                      className={`text-xs px-1.5 py-0.5 rounded ${
                        // @ts-ignore
                        creditAccount.test_clock.status === 'advancing'
                          ? 'bg-blue-100 text-blue-700'
                          : 'bg-green-100 text-green-700'
                      }`}
                    >
                      {/* @ts-ignore */}
                      {creditAccount.test_clock.status}
                    </div>
                  </div>
                  <Dialog open={testClockDialogOpen} onOpenChange={setTestClockDialogOpen}>
                    <DialogTrigger asChild>
                      <Button variant="outline" size="sm">
                        Advance
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="w-[400px]">
                      <DialogHeader>
                        <DialogTitle>Advance test clock</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <div className="flex items-center space-x-2 mb-2">
                            <Calendar className="h-4 w-4" />
                            <Label>Date</Label>
                          </div>
                          <DateTimePicker
                            date={testClockDateTime}
                            onDateChange={setTestClockDateTime}
                            placeholder="Select date and time"
                            className="mt-1"
                            inline
                          />
                        </div>
                      </div>
                      <DialogFooter>
                        <Button variant="outline" onClick={() => setTestClockDialogOpen(false)}>
                          Cancel
                        </Button>
                        <Button
                          loading={loadingActions['advance-clock']}
                          onClick={() =>
                            handleAsyncAction('advance-clock', () => {
                              if (!testClockDateTime) return Promise.resolve();
                              return handleAdvanceTestClock(testClockDateTime);
                            })
                          }
                          disabled={!testClockDateTime}
                        >
                          Advance
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              )}
            </div>
          </div>

          {/* Subscription Card */}
          <div className="rounded-lg border p-4 bg-card">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-lg font-semibold">Subscription</h3>
              <div className="flex items-center gap-1">
                {subscription && (
                  <>
                    {subscription.unpaid_invoice_url && subscription.provider !== 'apple' && (
                      <a
                        href={subscription.unpaid_invoice_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex"
                      >
                        <Button
                          variant="default"
                          size="sm"
                          className="bg-orange-500 hover:bg-orange-600"
                        >
                          Pay invoice
                        </Button>
                      </a>
                    )}
                    {subscription.provider !== 'apple' && (
                      <Button
                        variant="outline"
                        size="sm"
                        loading={loadingActions['billing-portal']}
                        onClick={() =>
                          handleAsyncAction('billing-portal', handleCreateBillingPortalSession)
                        }
                      >
                        Go to customer portal
                      </Button>
                    )}
                    {subscription?.external_id && subscription.provider !== 'apple' ? (
                      <a
                        href={`https://dashboard.stripe.com/test/subscriptions/${subscription.external_id}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex"
                      >
                        <Button
                          variant="icon"
                          size="sm"
                          className="opacity-60 hover:opacity-100"
                          title="Open in Stripe Dashboard"
                        >
                          <ExternalLink className="h-4 w-4" />
                        </Button>
                      </a>
                    ) : null}
                    <Button
                      variant="icon"
                      size="sm"
                      onClick={() => copyToClipboard(subscription.id, 'Subscription')}
                      className="opacity-60 hover:opacity-100"
                      title="Copy Subscription ID"
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </>
                )}
              </div>
            </div>

            {
              <div className="space-y-3">
                {subscription && (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <p className="text-sm text-muted-foreground">Status:</p>
                      <span
                        className={`text-xs px-2 py-1 rounded-full font-medium ${
                          subscription.status === 'active'
                            ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
                            : subscription.status === 'past_due'
                              ? 'bg-orange-100 text-orange-700 dark:bg-orange-900 dark:text-orange-300'
                              : subscription.status === 'incomplete'
                                ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-300'
                                : 'bg-gray-100 text-gray-700 dark:bg-gray-900 dark:text-gray-300'
                        }`}
                      >
                        {subscription.status}
                      </span>
                      {subscription.cancel_at_period_end && (
                        <div className="text-xs text-orange-600 bg-orange-100 px-1.5 py-0.5 rounded">
                          Canceling at period end
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {subscription?.current_period_start && subscription?.current_period_end && (
                  <div>
                    <p className="text-sm text-muted-foreground mb-2">Current billing period</p>
                    <div className="space-y-2">
                      <div className="flex justify-between text-xs">
                        <span>
                          {format(
                            new Date(subscription.current_period_start),
                            'yyyy-MM-dd HH:mm:ss',
                          )}
                        </span>
                        <span>
                          {format(new Date(subscription.current_period_end), 'yyyy-MM-dd HH:mm:ss')}
                        </span>
                      </div>
                      <div className="relative w-full bg-muted rounded-full h-2">
                        {(() => {
                          const now = creditAccount?.test_clock
                            ? // @ts-ignore
                              new Date(creditAccount.test_clock.frozen_time * 1000)
                            : new Date();
                          const start = new Date(subscription.current_period_start);
                          const end = new Date(subscription.current_period_end);
                          const totalDuration = end.getTime() - start.getTime();
                          const elapsed = now.getTime() - start.getTime();
                          const progress = Math.max(
                            0,
                            Math.min(100, (elapsed / totalDuration) * 100),
                          );

                          return (
                            <>
                              <div
                                className="bg-green-500 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${progress}%` }}
                              />
                              <div
                                className="absolute top-0 w-1 h-2 bg-green-700 rounded-full"
                                style={{ left: `${progress}%`, transform: 'translateX(-50%)' }}
                              />
                            </>
                          );
                        })()}
                      </div>
                    </div>
                  </div>
                )}

                {/* Apple Subscription Notice */}
                {subscription?.provider === 'apple' && (
                  <div className="bg-amber-50 border border-amber-200 rounded-lg p-3 mb-3">
                    <div className="flex items-start gap-2">
                      <div className="text-amber-600 mt-0.5">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path
                            fillRule="evenodd"
                            d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-amber-800">Apple Subscription</p>
                        <p className="text-sm text-amber-700 mt-1">
                          This subscription was purchased through Apple App Store. To manage your
                          subscription, please go to the App Store on your iOS device.
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Subscription Test Actions */}
                <div className="space-y-1.5">
                  <div
                    className={`grid gap-1.5 ${getAvailablePlans().length <= 2 ? 'grid-cols-2' : 'grid-cols-4'}`}
                  >
                    {subscription
                      ? getAvailablePlans().map(({ tier, interval }) => {
                          const actionKey = `change-${tier}-${interval}`;
                          const isAppleSubscription = subscription.provider === 'apple';
                          return (
                            <Button
                              key={`${tier}-${interval}`}
                              variant={
                                isCurrentPlan(subscription, tier, interval) ? 'default' : 'outline'
                              }
                              size="xs"
                              disabled={
                                isCurrentPlan(subscription, tier, interval) || isAppleSubscription
                              }
                              loading={loadingActions[actionKey]}
                              onClick={() =>
                                !isAppleSubscription &&
                                handleAsyncAction(actionKey, () =>
                                  handleUpdateSubscription(tier, interval),
                                )
                              }
                              className="text-xs"
                            >
                              {getButtonText(subscription, tier, interval)}
                            </Button>
                          );
                        })
                      : getAvailablePlans().map(({ tier, interval }) => {
                          const actionKey = `subscribe-${tier}-${interval}`;
                          const tierName = tier === 'pro' ? 'Pro' : 'Max';
                          const intervalName = interval === 'monthly' ? 'monthly' : 'yearly';
                          return (
                            <Button
                              key={`${tier}-${interval}`}
                              variant="outline"
                              size="xs"
                              loading={loadingActions[actionKey]}
                              onClick={() =>
                                handleAsyncAction(actionKey, () =>
                                  handleCreateSubscription(tier, interval),
                                )
                              }
                              className="text-xs"
                            >
                              Subscribe {tierName} {intervalName}
                            </Button>
                          );
                        })}
                  </div>

                  {subscription && (
                    <div className="grid grid-cols-2 gap-1.5">
                      <Button
                        variant="outline"
                        size="xs"
                        disabled={subscription.provider === 'apple'}
                        loading={loadingActions['cancel-at-period-end']}
                        onClick={() =>
                          subscription.provider !== 'apple' &&
                          handleAsyncAction('cancel-at-period-end', handleCancelSubscription)
                        }
                        className="text-xs"
                      >
                        Cancel at period end
                      </Button>
                      <Button
                        variant="destructive"
                        size="xs"
                        loading={loadingActions['cancel-now']}
                        onClick={() =>
                          handleAsyncAction('cancel-now', handleDeleteSubscriptionForTest)
                        }
                        className="text-xs"
                      >
                        Cancel right now
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            }
          </div>

          {/* Credit Account Card */}
          {creditAccount && (
            <div className="rounded-lg border p-4 bg-card">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-lg font-semibold">Credit account</h3>
                <div className="flex items-center gap-1">
                  <Dialog
                    open={rechargeCreditsDialogOpen}
                    onOpenChange={setRechargeCreditsDialogOpen}
                  >
                    <DialogTrigger asChild>
                      <Button variant="outline" size="sm">
                        Recharge
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="w-[400px]">
                      <DialogHeader>
                        <DialogTitle>Recharge credits</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label>Amount</Label>
                          <Input
                            type="number"
                            value={rechargeAmount}
                            onChange={(e) => setRechargeAmount(e.target.value)}
                            placeholder="Enter amount to recharge"
                            min="1"
                          />
                          <p className="text-xs text-muted-foreground mt-1">
                            Enter the number of credits to add to your account
                          </p>
                        </div>
                      </div>
                      <DialogFooter>
                        <Button
                          variant="outline"
                          onClick={() => setRechargeCreditsDialogOpen(false)}
                        >
                          Cancel
                        </Button>
                        <Button
                          loading={loadingActions['recharge-credits']}
                          onClick={() =>
                            handleAsyncAction('recharge-credits', () =>
                              handleRechargeCredits(parseInt(rechargeAmount)),
                            )
                          }
                          disabled={!rechargeAmount || parseInt(rechargeAmount) <= 0}
                        >
                          Recharge
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                  <Dialog
                    open={consumeCreditsDialogOpen}
                    onOpenChange={setConsumeCreditsDialogOpen}
                  >
                    <DialogTrigger asChild>
                      <Button variant="outline" size="sm">
                        Consume
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="w-[400px]">
                      <DialogHeader>
                        <DialogTitle>Consume credits</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label>Amount</Label>
                          <Input
                            type="number"
                            value={consumeAmount}
                            onChange={(e) => setConsumeAmount(e.target.value)}
                            placeholder="Enter amount to consume"
                            min="1"
                            max={creditAccount?.monthly_balance || 0}
                          />
                          <p className="text-xs text-muted-foreground mt-1">
                            Available balance: {creditAccount?.monthly_balance || 0}
                          </p>
                        </div>
                      </div>
                      <DialogFooter>
                        <Button
                          variant="outline"
                          onClick={() => setConsumeCreditsDialogOpen(false)}
                        >
                          Cancel
                        </Button>
                        <Button
                          loading={loadingActions['consume-credits']}
                          onClick={() =>
                            handleAsyncAction('consume-credits', () =>
                              handleConsumeCredits(parseInt(consumeAmount)),
                            )
                          }
                          // disabled={
                          //   !consumeAmount ||
                          //   parseInt(consumeAmount) <= 0 ||
                          //   parseInt(consumeAmount) > (creditAccount?.monthly_balance || 0)
                          // }
                        >
                          Consume
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                  <Button
                    variant="icon"
                    size="sm"
                    onClick={() => copyToClipboard(creditAccount.id, 'Credit Account')}
                    className="opacity-60 hover:opacity-100"
                    title="Copy ID"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <p className="text-sm text-muted-foreground">Product tier:</p>
                    <p className="font-medium text-sm">{creditAccount.product_tier}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <p className="text-sm text-muted-foreground">Monthly credits:</p>
                    <p className="text-sm font-medium">
                      {creditAccount.monthly_balance} / {creditAccount.monthly_quota}
                    </p>
                  </div>
                </div>

                <div className="w-full bg-muted rounded-full h-2">
                  <div
                    className="bg-primary h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${Math.min((creditAccount.monthly_balance / creditAccount.monthly_quota) * 100, 100)}%`,
                    }}
                  />
                </div>

                <div>
                  <p className="text-sm text-muted-foreground mb-2">Current credit period</p>
                  <div className="space-y-2">
                    <div className="flex justify-between text-xs">
                      <span>
                        {format(
                          new Date(creditAccount.current_period_start),
                          'yyyy-MM-dd HH:mm:ss',
                        )}
                      </span>
                      <span>
                        {format(new Date(creditAccount.current_period_end), 'yyyy-MM-dd HH:mm:ss')}
                      </span>
                    </div>
                    <div className="relative w-full bg-muted rounded-full h-2">
                      {(() => {
                        const now = creditAccount.test_clock
                          ? // @ts-ignore
                            new Date(creditAccount.test_clock.frozen_time * 1000)
                          : new Date();
                        const start = new Date(creditAccount.current_period_start);
                        const end = new Date(creditAccount.current_period_end);
                        const totalDuration = end.getTime() - start.getTime();
                        const elapsed = now.getTime() - start.getTime();
                        const progress = Math.max(
                          0,
                          Math.min(100, (elapsed / totalDuration) * 100),
                        );

                        return (
                          <>
                            <div
                              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${progress}%` }}
                            />
                            <div
                              className="absolute top-0 w-1 h-2 bg-blue-700 rounded-full"
                              style={{ left: `${progress}%`, transform: 'translateX(-50%)' }}
                            />
                          </>
                        );
                      })()}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Credit Transactions Card */}
          {creditTransactions.length > 0 && (
            <div className="rounded-lg border p-4 bg-card">
              <h3 className="text-lg font-semibold mb-3">Credit transactions</h3>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Balance</TableHead>
                      <TableHead>Metadata</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {paginatedTransactions.map((transaction) => (
                      <TableRow key={transaction.id} className="h-10">
                        <TableCell className="font-mono text-xs py-2">
                          {format(new Date(transaction.created_at), 'MM-dd HH:mm:ss')}
                        </TableCell>
                        <TableCell className="font-mono py-2">
                          <span
                            className={transaction.amount < 0 ? 'text-red-600' : 'text-green-600'}
                          >
                            {transaction.amount}
                          </span>
                        </TableCell>
                        <TableCell className="font-mono text-xs py-2">
                          {transaction.balance_before} → {transaction.balance_after}
                        </TableCell>
                        <TableCell className="py-2">
                          {transaction.metadata ? (
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-6 px-2 text-xs"
                              onClick={() => {
                                setSelectedTransaction(transaction);
                                setTransactionDetailDialogOpen(true);
                              }}
                            >
                              View Details
                            </Button>
                          ) : (
                            <span className="text-xs text-muted-foreground">No data</span>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination Controls */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-muted-foreground">
                    Showing {startIndex + 1} to {Math.min(endIndex, totalItems)} of {totalItems}{' '}
                    transactions
                  </div>
                  <div className="flex items-center gap-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                    >
                      <ChevronLeft className="h-4 w-4" />
                      Previous
                    </Button>

                    <div className="flex items-center gap-1 mx-2">
                      {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                        let pageNumber;
                        if (totalPages <= 5) {
                          pageNumber = i + 1;
                        } else if (currentPage <= 3) {
                          pageNumber = i + 1;
                        } else if (currentPage >= totalPages - 2) {
                          pageNumber = totalPages - 4 + i;
                        } else {
                          pageNumber = currentPage - 2 + i;
                        }

                        return (
                          <Button
                            key={pageNumber}
                            variant={currentPage === pageNumber ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setCurrentPage(pageNumber)}
                            className="w-8 h-8 p-0"
                          >
                            {pageNumber}
                          </Button>
                        );
                      })}
                    </div>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                    >
                      Next
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Transaction Metadata Dialog */}
          <Dialog open={transactionDetailDialogOpen} onOpenChange={setTransactionDetailDialogOpen}>
            <DialogContent className="w-[800px] max-w-[95vw] h-[600px] max-h-[90vh]">
              <DialogHeader>
                <DialogTitle>Transaction Metadata</DialogTitle>
              </DialogHeader>
              {selectedTransaction && (
                <div className="flex-1 overflow-hidden">
                  {selectedTransaction.metadata ? (
                    <pre className="text-sm p-4 bg-muted rounded-md overflow-auto h-full font-mono">
                      {JSON.stringify(selectedTransaction.metadata, null, 2)}
                    </pre>
                  ) : (
                    <div className="flex items-center justify-center h-full text-muted-foreground">
                      No metadata available for this transaction
                    </div>
                  )}
                </div>
              )}
              <DialogFooter>
                <Button variant="outline" onClick={() => setTransactionDetailDialogOpen(false)}>
                  Close
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Preview Invoice Confirmation Dialog */}
          <PreviewInvoiceDialog
            previewInvoice={previewInvoiceData}
            open={previewInvoiceDialogOpen}
            onOpenChange={setPreviewInvoiceDialogOpen}
            onConfirm={handleConfirmPreviewInvoice}
          />
        </div>
      </EmployeeOnly>
    </div>
  );
}
