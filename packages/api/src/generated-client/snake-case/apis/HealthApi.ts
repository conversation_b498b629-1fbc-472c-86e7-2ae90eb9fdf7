/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import * as runtime from '../../../runtime';
import type {
  HealthControllerHealth200Response,
  HealthControllerHealth503Response,
} from '../models/index';
import {
  HealthControllerHealth200ResponseFromJSON,
  HealthControllerHealth200ResponseToJSON,
  HealthControllerHealth503ResponseFromJSON,
  HealthControllerHealth503ResponseToJSON,
} from '../models/index';

/**
 * HealthApi - interface
 *
 * @export
 * @interface HealthApiInterface
 */
export interface HealthApiInterface {
  /**
   *
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof HealthApiInterface
   */
  healthRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<HealthControllerHealth200Response>>;

  /**
   */
  health(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<HealthControllerHealth200Response>;
}

/**
 *
 */
export class HealthApi extends runtime.BaseAPI implements HealthApiInterface {
  /**
   */
  async healthRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<HealthControllerHealth200Response>> {
    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    const urlPath = `/api/healthz`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'GET',
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      HealthControllerHealth200ResponseFromJSON(jsonValue),
    );
  }

  /**
   */
  async health(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<HealthControllerHealth200Response> {
    const response = await this.healthRaw(initOverrides);
    return await response.value();
  }
}
