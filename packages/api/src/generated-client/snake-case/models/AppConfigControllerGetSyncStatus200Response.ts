/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
/**
 *
 * @export
 * @interface AppConfigControllerGetSyncStatus200Response
 */
export interface AppConfigControllerGetSyncStatus200Response {
  /**
   *
   * @type {string}
   * @memberof AppConfigControllerGetSyncStatus200Response
   */
  status?: AppConfigControllerGetSyncStatus200ResponseStatusEnum;
  /**
   * Instance synchronization information
   * @type {object}
   * @memberof AppConfigControllerGetSyncStatus200Response
   */
  instance_info?: object;
  /**
   *
   * @type {string}
   * @memberof AppConfigControllerGetSyncStatus200Response
   */
  timestamp?: string;
}

/**
 * @export
 * @enum {string}
 */
export enum AppConfigControllerGetSyncStatus200ResponseStatusEnum {
  ok = 'ok',
  unknownDefaultOpenApi = '11184809',
}

/**
 * Check if a given object implements the AppConfigControllerGetSyncStatus200Response interface.
 */
export function instanceOfAppConfigControllerGetSyncStatus200Response(
  value: object,
): value is AppConfigControllerGetSyncStatus200Response {
  return true;
}

export function AppConfigControllerGetSyncStatus200ResponseFromJSON(
  json: any,
): AppConfigControllerGetSyncStatus200Response {
  return AppConfigControllerGetSyncStatus200ResponseFromJSONTyped(json, false);
}

export function AppConfigControllerGetSyncStatus200ResponseFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): AppConfigControllerGetSyncStatus200Response {
  if (json == null) {
    return json;
  }
  return {
    status: json.status == null ? undefined : json.status,
    instance_info: json.instance_info == null ? undefined : json.instance_info,
    timestamp: json.timestamp == null ? undefined : json.timestamp,
  };
}

export function AppConfigControllerGetSyncStatus200ResponseToJSON(
  json: any,
): AppConfigControllerGetSyncStatus200Response {
  return AppConfigControllerGetSyncStatus200ResponseToJSONTyped(json, false);
}

export function AppConfigControllerGetSyncStatus200ResponseToJSONTyped(
  value?: AppConfigControllerGetSyncStatus200Response | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    status: value.status,
    instance_info: value.instance_info,
    timestamp: value.timestamp,
  };
}
