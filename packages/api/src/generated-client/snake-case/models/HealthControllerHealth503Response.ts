/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
/**
 *
 * @export
 * @interface HealthControllerHealth503Response
 */
export interface HealthControllerHealth503Response {
  /**
   *
   * @type {string}
   * @memberof HealthControllerHealth503Response
   */
  status?: HealthControllerHealth503ResponseStatusEnum;
  /**
   *
   * @type {string}
   * @memberof HealthControllerHealth503Response
   */
  timestamp?: string;
  /**
   *
   * @type {string}
   * @memberof HealthControllerHealth503Response
   */
  error?: string;
}

/**
 * @export
 * @enum {string}
 */
export enum HealthControllerHealth503ResponseStatusEnum {
  error = 'error',
  unknownDefaultOpenApi = '11184809',
}

/**
 * Check if a given object implements the HealthControllerHealth503Response interface.
 */
export function instanceOfHealthControllerHealth503Response(
  value: object,
): value is HealthControllerHealth503Response {
  return true;
}

export function HealthControllerHealth503ResponseFromJSON(
  json: any,
): HealthControllerHealth503Response {
  return HealthControllerHealth503ResponseFromJSONTyped(json, false);
}

export function HealthControllerHealth503ResponseFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): HealthControllerHealth503Response {
  if (json == null) {
    return json;
  }
  return {
    status: json.status == null ? undefined : json.status,
    timestamp: json.timestamp == null ? undefined : json.timestamp,
    error: json.error == null ? undefined : json.error,
  };
}

export function HealthControllerHealth503ResponseToJSON(
  json: any,
): HealthControllerHealth503Response {
  return HealthControllerHealth503ResponseToJSONTyped(json, false);
}

export function HealthControllerHealth503ResponseToJSONTyped(
  value?: HealthControllerHealth503Response | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    status: value.status,
    timestamp: value.timestamp,
    error: value.error,
  };
}
