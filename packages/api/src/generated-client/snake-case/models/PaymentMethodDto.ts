/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { CardDto } from './CardDto';
import {
  CardDtoFromJSON,
  CardDtoFromJSONTyped,
  CardDtoToJSON,
  CardDtoToJSONTyped,
} from './CardDto';

/**
 *
 * @export
 * @interface PaymentMethodDto
 */
export interface PaymentMethodDto {
  /**
   * 支付方式类型
   * @type {string}
   * @memberof PaymentMethodDto
   */
  type: string;
  /**
   * 卡信息
   * @type {CardDto}
   * @memberof PaymentMethodDto
   */
  card: CardDto;
}

/**
 * Check if a given object implements the PaymentMethodDto interface.
 */
export function instanceOfPaymentMethodDto(value: object): value is PaymentMethodDto {
  if (!('type' in value) || value.type === undefined) return false;
  if (!('card' in value) || value.card === undefined) return false;
  return true;
}

export function PaymentMethodDtoFromJSON(json: any): PaymentMethodDto {
  return PaymentMethodDtoFromJSONTyped(json, false);
}

export function PaymentMethodDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): PaymentMethodDto {
  if (json == null) {
    return json;
  }
  return {
    type: json.type,
    card: CardDtoFromJSON(json.card),
  };
}

export function PaymentMethodDtoToJSON(json: any): PaymentMethodDto {
  return PaymentMethodDtoToJSONTyped(json, false);
}

export function PaymentMethodDtoToJSONTyped(
  value?: PaymentMethodDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    type: value.type,
    card: CardDtoToJSON(value.card),
  };
}
