/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
/**
 *
 * @export
 * @interface DuplicateBoardItemDto
 */
export interface DuplicateBoardItemDto {
  /**
   * BoardItem 标识
   * @type {string}
   * @memberof DuplicateBoardItemDto
   */
  board_item_id: string;
}

/**
 * Check if a given object implements the DuplicateBoardItemDto interface.
 */
export function instanceOfDuplicateBoardItemDto(value: object): value is DuplicateBoardItemDto {
  if (!('board_item_id' in value) || value.board_item_id === undefined) return false;
  return true;
}

export function DuplicateBoardItemDtoFromJSON(json: any): DuplicateBoardItemDto {
  return DuplicateBoardItemDtoFromJSONTyped(json, false);
}

export function DuplicateBoardItemDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): DuplicateBoardItemDto {
  if (json == null) {
    return json;
  }
  return {
    board_item_id: json.board_item_id,
  };
}

export function DuplicateBoardItemDtoToJSON(json: any): DuplicateBoardItemDto {
  return DuplicateBoardItemDtoToJSONTyped(json, false);
}

export function DuplicateBoardItemDtoToJSONTyped(
  value?: DuplicateBoardItemDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    board_item_id: value.board_item_id,
  };
}
