/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
/**
 *
 * @export
 * @interface HealthControllerHealth200ResponseChecks
 */
export interface HealthControllerHealth200ResponseChecks {
  /**
   *
   * @type {string}
   * @memberof HealthControllerHealth200ResponseChecks
   */
  database?: HealthControllerHealth200ResponseChecksDatabaseEnum;
  /**
   *
   * @type {string}
   * @memberof HealthControllerHealth200ResponseChecks
   */
  config?: HealthControllerHealth200ResponseChecksConfigEnum;
}

/**
 * @export
 * @enum {string}
 */
export enum HealthControllerHealth200ResponseChecksDatabaseEnum {
  ok = 'ok',
  unknownDefaultOpenApi = '11184809',
}
/**
 * @export
 * @enum {string}
 */
export enum HealthControllerHealth200ResponseChecksConfigEnum {
  ok = 'ok',
  unknownDefaultOpenApi = '11184809',
}

/**
 * Check if a given object implements the HealthControllerHealth200ResponseChecks interface.
 */
export function instanceOfHealthControllerHealth200ResponseChecks(
  value: object,
): value is HealthControllerHealth200ResponseChecks {
  return true;
}

export function HealthControllerHealth200ResponseChecksFromJSON(
  json: any,
): HealthControllerHealth200ResponseChecks {
  return HealthControllerHealth200ResponseChecksFromJSONTyped(json, false);
}

export function HealthControllerHealth200ResponseChecksFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): HealthControllerHealth200ResponseChecks {
  if (json == null) {
    return json;
  }
  return {
    database: json.database == null ? undefined : json.database,
    config: json.config == null ? undefined : json.config,
  };
}

export function HealthControllerHealth200ResponseChecksToJSON(
  json: any,
): HealthControllerHealth200ResponseChecks {
  return HealthControllerHealth200ResponseChecksToJSONTyped(json, false);
}

export function HealthControllerHealth200ResponseChecksToJSONTyped(
  value?: HealthControllerHealth200ResponseChecks | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    database: value.database,
    config: value.config,
  };
}
