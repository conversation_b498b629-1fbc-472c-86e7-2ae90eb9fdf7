/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
/**
 *
 * @export
 * @interface CardDto
 */
export interface CardDto {
  /**
   * 卡品牌
   * @type {string}
   * @memberof CardDto
   */
  brand: string;
  /**
   * 卡号
   * @type {string}
   * @memberof CardDto
   */
  last4: string;
}

/**
 * Check if a given object implements the CardDto interface.
 */
export function instanceOfCardDto(value: object): value is CardDto {
  if (!('brand' in value) || value.brand === undefined) return false;
  if (!('last4' in value) || value.last4 === undefined) return false;
  return true;
}

export function CardDtoFromJSON(json: any): CardDto {
  return CardDtoFromJSONTyped(json, false);
}

export function CardDtoFromJSONTyped(json: any, _ignoreDiscriminator: boolean): CardDto {
  if (json == null) {
    return json;
  }
  return {
    brand: json.brand,
    last4: json.last4,
  };
}

export function CardDtoToJSON(json: any): CardDto {
  return CardDtoToJSONTyped(json, false);
}

export function CardDtoToJSONTyped(
  value?: CardDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    brand: value.brand,
    last4: value.last4,
  };
}
