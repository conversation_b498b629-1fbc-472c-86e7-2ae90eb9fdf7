/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
/**
 *
 * @export
 * @interface AppConfigControllerRefreshConfig200Response
 */
export interface AppConfigControllerRefreshConfig200Response {
  /**
   *
   * @type {string}
   * @memberof AppConfigControllerRefreshConfig200Response
   */
  status?: AppConfigControllerRefreshConfig200ResponseStatusEnum;
  /**
   *
   * @type {string}
   * @memberof AppConfigControllerRefreshConfig200Response
   */
  message?: string;
  /**
   *
   * @type {string}
   * @memberof AppConfigControllerRefreshConfig200Response
   */
  timestamp?: string;
  /**
   * Instance sync information
   * @type {object}
   * @memberof AppConfigControllerRefreshConfig200Response
   */
  instanceInfo?: object;
}

/**
 * @export
 * @enum {string}
 */
export enum AppConfigControllerRefreshConfig200ResponseStatusEnum {
  ok = 'ok',
  unknownDefaultOpenApi = '11184809',
}

/**
 * Check if a given object implements the AppConfigControllerRefreshConfig200Response interface.
 */
export function instanceOfAppConfigControllerRefreshConfig200Response(
  value: object,
): value is AppConfigControllerRefreshConfig200Response {
  return true;
}

export function AppConfigControllerRefreshConfig200ResponseFromJSON(
  json: any,
): AppConfigControllerRefreshConfig200Response {
  return AppConfigControllerRefreshConfig200ResponseFromJSONTyped(json, false);
}

export function AppConfigControllerRefreshConfig200ResponseFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): AppConfigControllerRefreshConfig200Response {
  if (json == null) {
    return json;
  }
  return {
    status: json.status == null ? undefined : json.status,
    message: json.message == null ? undefined : json.message,
    timestamp: json.timestamp == null ? undefined : json.timestamp,
    instanceInfo: json.instanceInfo == null ? undefined : json.instanceInfo,
  };
}

export function AppConfigControllerRefreshConfig200ResponseToJSON(
  json: any,
): AppConfigControllerRefreshConfig200Response {
  return AppConfigControllerRefreshConfig200ResponseToJSONTyped(json, false);
}

export function AppConfigControllerRefreshConfig200ResponseToJSONTyped(
  value?: AppConfigControllerRefreshConfig200Response | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    status: value.status,
    message: value.message,
    timestamp: value.timestamp,
    instanceInfo: value.instanceInfo,
  };
}
