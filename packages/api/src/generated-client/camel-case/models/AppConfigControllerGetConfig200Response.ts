/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
/**
 *
 * @export
 * @interface AppConfigControllerGetConfig200Response
 */
export interface AppConfigControllerGetConfig200Response {
  /**
   *
   * @type {string}
   * @memberof AppConfigControllerGetConfig200Response
   */
  status?: AppConfigControllerGetConfig200ResponseStatusEnum;
  /**
   * Current application configuration
   * @type {object}
   * @memberof AppConfigControllerGetConfig200Response
   */
  config?: object;
  /**
   *
   * @type {string}
   * @memberof AppConfigControllerGetConfig200Response
   */
  timestamp?: string;
}

/**
 * @export
 * @enum {string}
 */
export enum AppConfigControllerGetConfig200ResponseStatusEnum {
  ok = 'ok',
  unknownDefaultOpenApi = '11184809',
}

/**
 * Check if a given object implements the AppConfigControllerGetConfig200Response interface.
 */
export function instanceOfAppConfigControllerGetConfig200Response(
  value: object,
): value is AppConfigControllerGetConfig200Response {
  return true;
}

export function AppConfigControllerGetConfig200ResponseFromJSON(
  json: any,
): AppConfigControllerGetConfig200Response {
  return AppConfigControllerGetConfig200ResponseFromJSONTyped(json, false);
}

export function AppConfigControllerGetConfig200ResponseFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): AppConfigControllerGetConfig200Response {
  if (json == null) {
    return json;
  }
  return {
    status: json.status == null ? undefined : json.status,
    config: json.config == null ? undefined : json.config,
    timestamp: json.timestamp == null ? undefined : json.timestamp,
  };
}

export function AppConfigControllerGetConfig200ResponseToJSON(
  json: any,
): AppConfigControllerGetConfig200Response {
  return AppConfigControllerGetConfig200ResponseToJSONTyped(json, false);
}

export function AppConfigControllerGetConfig200ResponseToJSONTyped(
  value?: AppConfigControllerGetConfig200Response | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    status: value.status,
    config: value.config,
    timestamp: value.timestamp,
  };
}
