/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { LineItemDto } from './LineItemDto';
import {
  LineItemDtoFromJSON,
  LineItemDtoFromJSONTyped,
  LineItemDtoToJSON,
  LineItemDtoToJSONTyped,
} from './LineItemDto';
import type { PaymentMethodDto } from './PaymentMethodDto';
import {
  PaymentMethodDtoFromJSON,
  PaymentMethodDtoFromJSONTyped,
  PaymentMethodDtoToJSON,
  PaymentMethodDtoToJSONTyped,
} from './PaymentMethodDto';

/**
 *
 * @export
 * @interface PreviewInvoiceDto
 */
export interface PreviewInvoiceDto {
  /**
   * 小计
   * @type {number}
   * @memberof PreviewInvoiceDto
   */
  subtotal: number;
  /**
   * 折扣
   * @type {number}
   * @memberof PreviewInvoiceDto
   */
  discount?: number;
  /**
   * 总计
   * @type {number}
   * @memberof PreviewInvoiceDto
   */
  total: number;
  /**
   * 预览账单项
   * @type {Array<LineItemDto>}
   * @memberof PreviewInvoiceDto
   */
  lines: Array<LineItemDto>;
  /**
   * 默认支付方式
   * @type {PaymentMethodDto}
   * @memberof PreviewInvoiceDto
   */
  defaultPaymentMethod?: PaymentMethodDto;
}

/**
 * Check if a given object implements the PreviewInvoiceDto interface.
 */
export function instanceOfPreviewInvoiceDto(value: object): value is PreviewInvoiceDto {
  if (!('subtotal' in value) || value.subtotal === undefined) return false;
  if (!('total' in value) || value.total === undefined) return false;
  if (!('lines' in value) || value.lines === undefined) return false;
  return true;
}

export function PreviewInvoiceDtoFromJSON(json: any): PreviewInvoiceDto {
  return PreviewInvoiceDtoFromJSONTyped(json, false);
}

export function PreviewInvoiceDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): PreviewInvoiceDto {
  if (json == null) {
    return json;
  }
  return {
    subtotal: json.subtotal,
    discount: json.discount == null ? undefined : json.discount,
    total: json.total,
    lines: (json.lines as Array<any>).map(LineItemDtoFromJSON),
    defaultPaymentMethod:
      json.defaultPaymentMethod == null
        ? undefined
        : PaymentMethodDtoFromJSON(json.defaultPaymentMethod),
  };
}

export function PreviewInvoiceDtoToJSON(json: any): PreviewInvoiceDto {
  return PreviewInvoiceDtoToJSONTyped(json, false);
}

export function PreviewInvoiceDtoToJSONTyped(
  value?: PreviewInvoiceDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    subtotal: value.subtotal,
    discount: value.discount,
    total: value.total,
    lines: (value.lines as Array<any>).map(LineItemDtoToJSON),
    defaultPaymentMethod: PaymentMethodDtoToJSON(value.defaultPaymentMethod),
  };
}
