/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { HealthControllerHealth200ResponseChecks } from './HealthControllerHealth200ResponseChecks';
import {
  HealthControllerHealth200ResponseChecksFromJSON,
  HealthControllerHealth200ResponseChecksFromJSONTyped,
  HealthControllerHealth200ResponseChecksToJSON,
  HealthControllerHealth200ResponseChecksToJSONTyped,
} from './HealthControllerHealth200ResponseChecks';

/**
 *
 * @export
 * @interface HealthControllerHealth200Response
 */
export interface HealthControllerHealth200Response {
  /**
   *
   * @type {string}
   * @memberof HealthControllerHealth200Response
   */
  status?: HealthControllerHealth200ResponseStatusEnum;
  /**
   *
   * @type {string}
   * @memberof HealthControllerHealth200Response
   */
  timestamp?: string;
  /**
   *
   * @type {HealthControllerHealth200ResponseChecks}
   * @memberof HealthControllerHealth200Response
   */
  checks?: HealthControllerHealth200ResponseChecks;
}

/**
 * @export
 * @enum {string}
 */
export enum HealthControllerHealth200ResponseStatusEnum {
  ok = 'ok',
  unknownDefaultOpenApi = '11184809',
}

/**
 * Check if a given object implements the HealthControllerHealth200Response interface.
 */
export function instanceOfHealthControllerHealth200Response(
  value: object,
): value is HealthControllerHealth200Response {
  return true;
}

export function HealthControllerHealth200ResponseFromJSON(
  json: any,
): HealthControllerHealth200Response {
  return HealthControllerHealth200ResponseFromJSONTyped(json, false);
}

export function HealthControllerHealth200ResponseFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): HealthControllerHealth200Response {
  if (json == null) {
    return json;
  }
  return {
    status: json.status == null ? undefined : json.status,
    timestamp: json.timestamp == null ? undefined : json.timestamp,
    checks:
      json.checks == null
        ? undefined
        : HealthControllerHealth200ResponseChecksFromJSON(json.checks),
  };
}

export function HealthControllerHealth200ResponseToJSON(
  json: any,
): HealthControllerHealth200Response {
  return HealthControllerHealth200ResponseToJSONTyped(json, false);
}

export function HealthControllerHealth200ResponseToJSONTyped(
  value?: HealthControllerHealth200Response | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    status: value.status,
    timestamp: value.timestamp,
    checks: HealthControllerHealth200ResponseChecksToJSON(value.checks),
  };
}
