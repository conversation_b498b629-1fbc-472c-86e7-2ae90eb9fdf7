/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
/**
 *
 * @export
 * @interface LineItemDto
 */
export interface LineItemDto {
  /**
   * 账单项标题
   * @type {string}
   * @memberof LineItemDto
   */
  title: string;
  /**
   * 账单项描述
   * @type {string}
   * @memberof LineItemDto
   */
  description: string;
  /**
   * 账单项金额
   * @type {number}
   * @memberof LineItemDto
   */
  amount: number;
}

/**
 * Check if a given object implements the LineItemDto interface.
 */
export function instanceOfLineItemDto(value: object): value is LineItemDto {
  if (!('title' in value) || value.title === undefined) return false;
  if (!('description' in value) || value.description === undefined) return false;
  if (!('amount' in value) || value.amount === undefined) return false;
  return true;
}

export function LineItemDtoFromJSON(json: any): LineItemDto {
  return LineItemDtoFromJSONTyped(json, false);
}

export function LineItemDtoFromJSONTyped(json: any, _ignoreDiscriminator: boolean): LineItemDto {
  if (json == null) {
    return json;
  }
  return {
    title: json.title,
    description: json.description,
    amount: json.amount,
  };
}

export function LineItemDtoToJSON(json: any): LineItemDto {
  return LineItemDtoToJSONTyped(json, false);
}

export function LineItemDtoToJSONTyped(
  value?: LineItemDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    title: value.title,
    description: value.description,
    amount: value.amount,
  };
}
