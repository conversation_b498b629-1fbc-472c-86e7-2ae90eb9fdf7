/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
/**
 *
 * @export
 * @interface DuplicateBoardItemDto
 */
export interface DuplicateBoardItemDto {
  /**
   * BoardItem 标识
   * @type {string}
   * @memberof DuplicateBoardItemDto
   */
  boardItemId: string;
}

/**
 * Check if a given object implements the DuplicateBoardItemDto interface.
 */
export function instanceOfDuplicateBoardItemDto(value: object): value is DuplicateBoardItemDto {
  if (!('boardItemId' in value) || value.boardItemId === undefined) return false;
  return true;
}

export function DuplicateBoardItemDtoFromJSON(json: any): DuplicateBoardItemDto {
  return DuplicateBoardItemDtoFromJSONTyped(json, false);
}

export function DuplicateBoardItemDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): DuplicateBoardItemDto {
  if (json == null) {
    return json;
  }
  return {
    boardItemId: json.boardItemId,
  };
}

export function DuplicateBoardItemDtoToJSON(json: any): DuplicateBoardItemDto {
  return DuplicateBoardItemDtoToJSONTyped(json, false);
}

export function DuplicateBoardItemDtoToJSONTyped(
  value?: DuplicateBoardItemDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    boardItemId: value.boardItemId,
  };
}
