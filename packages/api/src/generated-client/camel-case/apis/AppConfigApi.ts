/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import * as runtime from '../../../runtime';
import type {
  AppConfigControllerGetConfig200Response,
  AppConfigControllerGetSyncStatus200Response,
  AppConfigControllerRefreshConfig200Response,
} from '../models/index';
import {
  AppConfigControllerGetConfig200ResponseFromJSON,
  AppConfigControllerGetConfig200ResponseToJSON,
  AppConfigControllerGetSyncStatus200ResponseFromJSON,
  AppConfigControllerGetSyncStatus200ResponseToJSON,
  AppConfigControllerRefreshConfig200ResponseFromJSON,
  AppConfigControllerRefreshConfig200ResponseToJSON,
} from '../models/index';

export interface AppConfigControllerRefreshConfigRequest {
  xDopplerSignature?: string;
}

/**
 * AppConfigApi - interface
 *
 * @export
 * @interface AppConfigApiInterface
 */
export interface AppConfigApiInterface {
  /**
   *
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AppConfigApiInterface
   */
  getConfigRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<AppConfigControllerGetConfig200Response>>;

  /**
   */
  getConfig(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<AppConfigControllerGetConfig200Response>;

  /**
   *
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AppConfigApiInterface
   */
  getSyncStatusRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<AppConfigControllerGetSyncStatus200Response>>;

  /**
   */
  getSyncStatus(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<AppConfigControllerGetSyncStatus200Response>;

  /**
   *
   * @param {string} [xDopplerSignature] Doppler webhook signature for request verification
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AppConfigApiInterface
   */
  refreshConfigRaw(
    requestParameters: AppConfigControllerRefreshConfigRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<AppConfigControllerRefreshConfig200Response>>;

  /**
   */
  refreshConfig(
    xDopplerSignature?: string,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<AppConfigControllerRefreshConfig200Response>;
}

/**
 *
 */
export class AppConfigApi extends runtime.BaseAPI implements AppConfigApiInterface {
  /**
   */
  async getConfigRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<AppConfigControllerGetConfig200Response>> {
    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    const urlPath = `/api/app-config`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'GET',
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      AppConfigControllerGetConfig200ResponseFromJSON(jsonValue),
    );
  }

  /**
   */
  async getConfig(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<AppConfigControllerGetConfig200Response> {
    const response = await this.getConfigRaw(initOverrides);
    return await response.value();
  }

  /**
   */
  async getSyncStatusRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<AppConfigControllerGetSyncStatus200Response>> {
    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    const urlPath = `/api/app-config/sync-status`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'GET',
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      AppConfigControllerGetSyncStatus200ResponseFromJSON(jsonValue),
    );
  }

  /**
   */
  async getSyncStatus(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<AppConfigControllerGetSyncStatus200Response> {
    const response = await this.getSyncStatusRaw(initOverrides);
    return await response.value();
  }

  /**
   */
  async refreshConfigRaw(
    requestParameters: AppConfigControllerRefreshConfigRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<AppConfigControllerRefreshConfig200Response>> {
    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    if (requestParameters.xDopplerSignature != null) {
      headerParameters['x-doppler-signature'] = String(requestParameters.xDopplerSignature);
    }

    const urlPath = `/api/app-config/refresh`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      AppConfigControllerRefreshConfig200ResponseFromJSON(jsonValue),
    );
  }

  /**
   */
  async refreshConfig(
    xDopplerSignature?: string,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<AppConfigControllerRefreshConfig200Response> {
    const response = await this.refreshConfigRaw(
      { xDopplerSignature: xDopplerSignature },
      initOverrides,
    );
    return await response.value();
  }
}
