/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import * as runtime from '../../../runtime';
import type {
  BoardItemDto,
  DuplicateBoardItemDto,
  MoveBoardItemToBoardGroupDto,
  MoveBoardItemToRootDto,
} from '../models/index';
import {
  BoardItemDtoFromJSON,
  BoardItemDtoToJSON,
  DuplicateBoardItemDtoFromJSON,
  DuplicateBoardItemDtoToJSON,
  MoveBoardItemToBoardGroupDtoFromJSON,
  MoveBoardItemToBoardGroupDtoToJSON,
  MoveBoardItemToRootDtoFromJSON,
  MoveBoardItemToRootDtoToJSON,
} from '../models/index';

export interface BoardItemControllerDuplicateRequest {
  duplicateBoardItemDto: DuplicateBoardItemDto;
}

export interface BoardItemControllerMoveBoardItemToBoardGroupRequest {
  moveBoardItemToBoardGroupDto: MoveBoardItemToBoardGroupDto;
}

export interface BoardItemControllerMoveBoardItemToRootRequest {
  moveBoardItemToRootDto: MoveBoardItemToRootDto;
}

/**
 * BoardItemApi - interface
 *
 * @export
 * @interface BoardItemApiInterface
 */
export interface BoardItemApiInterface {
  /**
   * 此 API 已废弃但保持兼容。复制一个 BoardItem，支持 Snip、Thought、BoardGroup 类型
   * @summary [DEPRECATED] Duplicate BoardItem
   * @param {DuplicateBoardItemDto} duplicateBoardItemDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BoardItemApiInterface
   */
  duplicateRaw(
    requestParameters: BoardItemControllerDuplicateRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<BoardItemDto>>;

  /**
   * 此 API 已废弃但保持兼容。复制一个 BoardItem，支持 Snip、Thought、BoardGroup 类型
   * [DEPRECATED] Duplicate BoardItem
   */
  duplicate(
    duplicateBoardItemDto: DuplicateBoardItemDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<BoardItemDto>;

  /**
   * 此 API 已废弃但保持兼容，内部使用新的统一移动服务实现。建议迁移到 /api/v1/items/move
   * @summary [DEPRECATED] Move BoardItem to BoardGroup
   * @param {MoveBoardItemToBoardGroupDto} moveBoardItemToBoardGroupDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BoardItemApiInterface
   */
  moveBoardItemToBoardGroupRaw(
    requestParameters: BoardItemControllerMoveBoardItemToBoardGroupRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>>;

  /**
   * 此 API 已废弃但保持兼容，内部使用新的统一移动服务实现。建议迁移到 /api/v1/items/move
   * [DEPRECATED] Move BoardItem to BoardGroup
   */
  moveBoardItemToBoardGroup(
    moveBoardItemToBoardGroupDto: MoveBoardItemToBoardGroupDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void>;

  /**
   * 此 API 已废弃但保持兼容，内部使用新的统一移动服务实现。建议迁移到 /api/v1/items/move
   * @summary [DEPRECATED] Move BoardItem to Root
   * @param {MoveBoardItemToRootDto} moveBoardItemToRootDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BoardItemApiInterface
   */
  moveBoardItemToRootRaw(
    requestParameters: BoardItemControllerMoveBoardItemToRootRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>>;

  /**
   * 此 API 已废弃但保持兼容，内部使用新的统一移动服务实现。建议迁移到 /api/v1/items/move
   * [DEPRECATED] Move BoardItem to Root
   */
  moveBoardItemToRoot(
    moveBoardItemToRootDto: MoveBoardItemToRootDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void>;
}

/**
 *
 */
export class BoardItemApi extends runtime.BaseAPI implements BoardItemApiInterface {
  /**
   * 此 API 已废弃但保持兼容。复制一个 BoardItem，支持 Snip、Thought、BoardGroup 类型
   * [DEPRECATED] Duplicate BoardItem
   */
  async duplicateRaw(
    requestParameters: BoardItemControllerDuplicateRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<BoardItemDto>> {
    if (requestParameters.duplicateBoardItemDto == null) {
      throw new runtime.RequiredError(
        'duplicateBoardItemDto',
        'Required parameter "duplicateBoardItemDto" was null or undefined when calling duplicate().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/boardItem/duplicate`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: DuplicateBoardItemDtoToJSON(requestParameters.duplicateBoardItemDto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) => BoardItemDtoFromJSON(jsonValue));
  }

  /**
   * 此 API 已废弃但保持兼容。复制一个 BoardItem，支持 Snip、Thought、BoardGroup 类型
   * [DEPRECATED] Duplicate BoardItem
   */
  async duplicate(
    duplicateBoardItemDto: DuplicateBoardItemDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<BoardItemDto> {
    const response = await this.duplicateRaw(
      { duplicateBoardItemDto: duplicateBoardItemDto },
      initOverrides,
    );
    return await response.value();
  }

  /**
   * 此 API 已废弃但保持兼容，内部使用新的统一移动服务实现。建议迁移到 /api/v1/items/move
   * [DEPRECATED] Move BoardItem to BoardGroup
   */
  async moveBoardItemToBoardGroupRaw(
    requestParameters: BoardItemControllerMoveBoardItemToBoardGroupRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>> {
    if (requestParameters.moveBoardItemToBoardGroupDto == null) {
      throw new runtime.RequiredError(
        'moveBoardItemToBoardGroupDto',
        'Required parameter "moveBoardItemToBoardGroupDto" was null or undefined when calling moveBoardItemToBoardGroup().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/boardItem/moveBoardItemToBoardGroup`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: MoveBoardItemToBoardGroupDtoToJSON(requestParameters.moveBoardItemToBoardGroupDto),
      },
      initOverrides,
    );

    return new runtime.VoidApiResponse(response);
  }

  /**
   * 此 API 已废弃但保持兼容，内部使用新的统一移动服务实现。建议迁移到 /api/v1/items/move
   * [DEPRECATED] Move BoardItem to BoardGroup
   */
  async moveBoardItemToBoardGroup(
    moveBoardItemToBoardGroupDto: MoveBoardItemToBoardGroupDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void> {
    await this.moveBoardItemToBoardGroupRaw(
      { moveBoardItemToBoardGroupDto: moveBoardItemToBoardGroupDto },
      initOverrides,
    );
  }

  /**
   * 此 API 已废弃但保持兼容，内部使用新的统一移动服务实现。建议迁移到 /api/v1/items/move
   * [DEPRECATED] Move BoardItem to Root
   */
  async moveBoardItemToRootRaw(
    requestParameters: BoardItemControllerMoveBoardItemToRootRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>> {
    if (requestParameters.moveBoardItemToRootDto == null) {
      throw new runtime.RequiredError(
        'moveBoardItemToRootDto',
        'Required parameter "moveBoardItemToRootDto" was null or undefined when calling moveBoardItemToRoot().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/boardItem/moveBoardItemToRoot`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: MoveBoardItemToRootDtoToJSON(requestParameters.moveBoardItemToRootDto),
      },
      initOverrides,
    );

    return new runtime.VoidApiResponse(response);
  }

  /**
   * 此 API 已废弃但保持兼容，内部使用新的统一移动服务实现。建议迁移到 /api/v1/items/move
   * [DEPRECATED] Move BoardItem to Root
   */
  async moveBoardItemToRoot(
    moveBoardItemToRootDto: MoveBoardItemToRootDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void> {
    await this.moveBoardItemToRootRaw(
      { moveBoardItemToRootDto: moveBoardItemToRootDto },
      initOverrides,
    );
  }
}
