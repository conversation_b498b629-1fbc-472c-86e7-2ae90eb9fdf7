'use client';

import {
  CreateSubscriptionDto,
  CreateSubscriptionResponseDto,
  PreviewInvoiceDto,
  BillingInterval as SubscriptionBillingIntervalEnum,
  SubscriptionProductTier as SubscriptionProductTierEnum,
  UpdateSubscriptionDto,
  UpdateSubscriptionResponseDto,
} from '@repo/api/generated-client/snake-case/index';
import { formatThousands } from '@repo/common';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@repo/ui/components/ui/tabs';
import { creditAccountAtom, subscriptionAtom } from '@repo/ui/hooks/useSubscription';
import { cn } from '@repo/ui/lib/utils';
import { format } from 'date-fns';
import { useAtom } from 'jotai';
import { Calendar, Check } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Button } from '../../ui/button';
import { FreeIcon } from './free';
import styles from './index.module.css';
import { MaxIcon } from './max';
import { PreviewInvoiceDialog } from './preview-invoice-dialog';
import { ProIcon } from './pro';
import { UnpaidDialog } from './unpaid-dialog';

interface SubscriptionProps {
  signedIn: boolean;
  onSignIn?: () => void;
  onCreateSubscription: (
    createSubscriptionDto: CreateSubscriptionDto,
  ) => Promise<CreateSubscriptionResponseDto | undefined>;
  onUpdateSubscription: (
    updateSubscriptionDto: UpdateSubscriptionDto,
  ) => Promise<UpdateSubscriptionResponseDto | undefined>;
}

const CheckItem = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="body flex items-baseline gap-2 text-secondary-fg">
      <Check size={14} className="relative top-1" />
      <span className="flex-1 flex items-center gap-1">{children}</span>
    </div>
  );
};

// const DotItem = ({
//   children,
//   size = 'default',
// }: {
//   children: React.ReactNode;
//   size?: 'default' | 'sm';
// }) => {
//   return (
//     <div
//       className={cn(
//         'ym-pricing-dot-item text-secondary-fg flex items-baseline gap-2',
//         size === 'sm' && 'gap-0 text-foreground',
//       )}
//     >
//       {size === 'sm' ? <Dot size={14} /> : <DotIcon size={14} className="relative top-[2px]" />}
//       <span className={cn('flex-1', size === 'sm' && 'text-sm')}>{children}</span>
//     </div>
//   );
// };

const AnnualMonthlyPlan = ({
  signedIn,
  type,
  onUpgrade,
}: {
  signedIn: boolean;
  type: SubscriptionBillingIntervalEnum;
  onUpgrade?: (
    productTier: SubscriptionProductTierEnum,
    billingInterval: SubscriptionBillingIntervalEnum,
  ) => Promise<void>;
}) => {
  const [subscription] = useAtom(subscriptionAtom);
  const currentPeriodEnd = subscription?.current_period_end;
  const billingInterval = subscription?.billing_interval;
  const productTier = subscription?.product_tier;
  const renewChange = subscription?.renew_change;
  const unpaidInvoiceUrl = subscription?.unpaid_invoice_url;
  const isAppleSubscription = subscription?.provider === 'apple';

  const [proLoading, setProLoading] = useState(false);
  const [maxLoading, setMaxLoading] = useState(false);
  const isAnnually = type === SubscriptionBillingIntervalEnum.yearly;
  const isMonthly = type === SubscriptionBillingIntervalEnum.monthly;
  const current =
    (isAnnually &&
      (renewChange?.billing_interval === SubscriptionBillingIntervalEnum.yearly ||
        (!renewChange && billingInterval === SubscriptionBillingIntervalEnum.yearly))) ||
    (isMonthly &&
      (renewChange?.billing_interval === SubscriptionBillingIntervalEnum.monthly ||
        (!renewChange && billingInterval === SubscriptionBillingIntervalEnum.monthly)));

  const currentPro =
    current &&
    (renewChange?.product_tier === SubscriptionProductTierEnum.pro ||
      (!renewChange && productTier === SubscriptionProductTierEnum.pro));
  const currentMax =
    current &&
    (renewChange?.product_tier === SubscriptionProductTierEnum.max ||
      (!renewChange && productTier === SubscriptionProductTierEnum.max));

  const isFree = !productTier;
  const isUnpaid = !!unpaidInvoiceUrl;
  const isRenewChange = !!renewChange;

  const handleLoading = async (fn: () => Promise<void>, tier: SubscriptionProductTierEnum) => {
    if (tier === SubscriptionProductTierEnum.pro) {
      setProLoading(true);
    } else {
      setMaxLoading(true);
    }
    try {
      await fn();
    } catch (error) {
      console.error(error);
    } finally {
      if (tier === SubscriptionProductTierEnum.pro) {
        setProLoading(false);
      } else {
        setMaxLoading(false);
      }
    }
  };

  const renderRenewChange = () => {
    return (
      <>
        {currentPeriodEnd && (
          <p className="body text-caption-fg flex items-center gap-1 mt-2">
            <Calendar size={14} />
            Starts on {format(currentPeriodEnd, 'MMM dd, yyyy')} (next billing date).
          </p>
        )}
      </>
    );
  };

  const renderUnpaid = () => {
    return (
      <p className="bg-snip-card text-secondary-fg body px-4 py-3 rounded-xl mt-2">
        You have an unpaid order. If payment is not completed within 24 hours, the order will be
        automatically canceled.{' '}
        <a
          href={unpaidInvoiceUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="text-function-link"
        >
          Pay now
        </a>
      </p>
    );
  };

  return (
    <div className="flex flex-col items-center justify-center">
      <div
        className={cn(
          'grid grid-cols-1 md:grid-cols-3 gap-4 w-full mx-auto max-w-[1200px]',
          !isFree && 'md:grid-cols-2 max-w-[800px]',
        )}
      >
        {isFree && (
          <div className="p-8 bg-background border border-snip-card rounded-2xl">
            <FreeIcon className="mb-5" size={48} />
            <div className={cn('mb-8 font-sans-title', styles.pricingTier)}>Free</div>
            <div className="flex gap-x-1 mb-4">
              <span className={cn(styles.currency, 'text-caption-fg')}>$</span>
              <span className={cn(styles.title, 'font-sans-title')}>0</span>
            </div>
            <div className="paragraph mb-6">Try YouMind</div>
            <Button
              className={cn(
                'w-full h-10 title',
                signedIn && isFree && 'text-disabled-fg bg-select disabled:opacity-100',
              )}
              disabled={signedIn && isFree}
              onClick={() => {
                handleLoading(async () => {
                  await onUpgrade?.(
                    SubscriptionProductTierEnum.unknownDefaultOpenApi,
                    SubscriptionBillingIntervalEnum.unknownDefaultOpenApi,
                  );
                }, SubscriptionProductTierEnum.unknownDefaultOpenApi);
              }}
            >
              {signedIn ? 'Your current plan' : 'Get started'}
            </Button>
            <div className="flex flex-col gap-2 mt-6">
              <CheckItem>2,000 credits per month</CheckItem>
              <CheckItem>Limited access to AI models</CheckItem>
              <CheckItem>Save up to 100 materials</CheckItem>
              <CheckItem>Limited AI features</CheckItem>
            </div>
          </div>
        )}
        <div className="p-8 bg-background border border-snip-card rounded-2xl">
          <div className="flex items-start justify-between mb-5">
            <ProIcon size={48} />
          </div>
          <div className={cn('mb-8 font-sans-title', styles.pricingTier)}>Pro</div>
          <div className="flex gap-x-2 mb-4">
            <div className="flex gap-x-1">
              <span className={cn(styles.currency, 'text-caption-fg')}>$</span>
              <span className={cn(styles.title, 'font-sans-title')}>
                {isAnnually ? '200' : '20'}
              </span>
            </div>
            <div className={cn(styles.unit, 'text-caption-fg relative top-1')}>
              USD/
              <br />
              {isAnnually ? 'year' : 'month'}
            </div>
          </div>
          <div className="paragraph mb-6">For everyday productivity</div>
          <Button
            loading={proLoading}
            disabled={currentPro || maxLoading || isAppleSubscription || isUnpaid}
            className={cn(
              'w-full h-10 title',
              (currentPro || proLoading) && 'text-disabled-fg bg-select disabled:opacity-100',
            )}
            onClick={() => {
              handleLoading(async () => {
                await onUpgrade?.(SubscriptionProductTierEnum.pro, type);
              }, SubscriptionProductTierEnum.pro);
            }}
          >
            {currentPro ? 'Your current plan' : 'Get Pro'}
          </Button>

          {currentPro && isRenewChange && renderRenewChange()}
          {currentPro && isUnpaid && renderUnpaid()}

          <div className="flex flex-col mt-6">
            <div className="flex flex-col gap-2">
              <CheckItem>20,000 credits per month</CheckItem>
              <CheckItem>Unlimited access to AI models</CheckItem>
              <CheckItem>Unlimited materials</CheckItem>
              <CheckItem>Unlimited upload and parsing of files</CheckItem>
              <CheckItem>Unlimited AI writing</CheckItem>
              <CheckItem>Image creation</CheckItem>
              <CheckItem>Audio generation</CheckItem>
              <CheckItem>High memory and context</CheckItem>
            </div>
          </div>
        </div>
        <div className="p-8 bg-background border border-snip-card rounded-2xl">
          <MaxIcon className="mb-5" size={48} />
          <div className={cn('mb-8 font-sans-title', styles.pricingTier)}>Max</div>
          <div className="flex gap-x-2 mb-4">
            <div className="flex gap-x-1">
              <span className={cn(styles.currency, 'text-caption-fg')}>$</span>
              <span className={cn(styles.title, 'font-sans-title')}>
                {isAnnually ? formatThousands(1000) : '100'}
              </span>
            </div>
            <div className={cn(styles.unit, 'text-caption-fg relative top-1')}>
              USD/
              <br />
              {isAnnually ? 'year' : 'month'}
            </div>
          </div>
          <div className="paragraph mb-6">Get the most out of YouMind</div>
          <Button
            loading={maxLoading}
            disabled={currentMax || proLoading || isAppleSubscription || isUnpaid}
            className={cn(
              'w-full h-10 title',
              (currentMax || maxLoading) && 'text-disabled-fg bg-select disabled:opacity-100',
            )}
            onClick={() => {
              handleLoading(async () => {
                await onUpgrade?.(SubscriptionProductTierEnum.max, type);
              }, SubscriptionProductTierEnum.max);
            }}
          >
            {currentMax ? 'Your current plan' : 'Get Max'}
          </Button>

          {currentMax && isRenewChange && renderRenewChange()}
          {currentMax && isUnpaid && renderUnpaid()}

          {isAppleSubscription && currentPro && (
            <p className="bg-snip-card text-secondary-fg body px-4 py-3 rounded-xl relative top-[-8px] mb-2">
              You purchased subscription on iOS. Use the YouMind app to{' '}
              <a
                href="https://support.apple.com/en-us/118428"
                target="_blank"
                rel="noopener noreferrer"
                className="text-function-link"
              >
                upgrade it in Apple.
              </a>
            </p>
          )}
          <div className="flex flex-col mt-6">
            <div className="flex flex-col gap-2">
              <CheckItem>200,000 credits per month</CheckItem>
              <CheckItem>Unlimited access to AI models</CheckItem>
              <CheckItem>Unlimited materials</CheckItem>
              <CheckItem>Unlimited upload and parsing of large files</CheckItem>
              <CheckItem>Maximum and faster AI writing</CheckItem>
              <CheckItem>Maximum image creation</CheckItem>
              <CheckItem>Maximum audio creation</CheckItem>
              <CheckItem>Maximum memory and context</CheckItem>
              <CheckItem>Advanced customization and automation</CheckItem>
              <CheckItem>Early access to new features</CheckItem>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export const Subscription: React.FC<SubscriptionProps> = ({
  signedIn,
  onCreateSubscription,
  onUpdateSubscription,
  onSignIn,
}) => {
  const [subscription, setSubscription] = useAtom(subscriptionAtom);
  const [, setCreditAccount] = useAtom(creditAccountAtom);
  const [previewInvoice, setPreviewInvoice] = useState<PreviewInvoiceDto | null>(null);
  const [pendingSubscriptionUpdate, setPendingSubscriptionUpdate] = useState<{
    product_tier: SubscriptionProductTierEnum;
    billing_interval: SubscriptionBillingIntervalEnum;
  } | null>(null);
  const [unpaidDialogOpen, setUnpaidDialogOpen] = useState(false);
  const [previewInvoiceDialogOpen, setPreviewInvoiceDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const isAppleSubscription = subscription?.provider === 'apple';
  const [tabValue, setTabValue] = useState<SubscriptionBillingIntervalEnum>(
    SubscriptionBillingIntervalEnum.yearly,
  );

  useEffect(() => {
    const tabValue =
      subscription?.renew_change?.billing_interval ||
      (!subscription?.renew_change && subscription?.billing_interval) ||
      SubscriptionBillingIntervalEnum.yearly;
    setTabValue(tabValue);
  }, [subscription]);

  const handleOnPreviewOrUpgrade = async (
    tier: SubscriptionProductTierEnum,
    interval: SubscriptionBillingIntervalEnum,
  ) => {
    if (!signedIn) {
      onSignIn?.();
      return;
    }

    setLoading(true);
    try {
      if (!isAppleSubscription) {
        let data: CreateSubscriptionResponseDto | UpdateSubscriptionResponseDto | undefined;
        if (!subscription) {
          data = await onCreateSubscription({
            product_tier: tier,
            billing_interval: interval,
          });
        } else {
          data = await onUpdateSubscription({
            product_tier: tier,
            billing_interval: interval,
            preview_if_upgrade: true, // 升级时先预览账单
          });
        }
        if (data) {
          const { preview_invoice } = data as UpdateSubscriptionResponseDto;
          if (preview_invoice) {
            setPreviewInvoice(preview_invoice);
            setPendingSubscriptionUpdate({ product_tier: tier, billing_interval: interval });
            setPreviewInvoiceDialogOpen(true);
            return;
          }

          if (data.subscription) {
            setSubscription(data.subscription);

            if (data.subscription.unpaid_invoice_url) {
              setUnpaidDialogOpen(true);
            }
          }
          if (data.credit_account) {
            setCreditAccount(data.credit_account);
          }
          if (data.redirect) {
            window.location.href = data.redirect;
          }
        }
      }
    } finally {
      setLoading(false);
    }
  };

  const handleOnUpgrade = async () => {
    try {
      if (!isAppleSubscription && pendingSubscriptionUpdate) {
        const data = await onUpdateSubscription({
          product_tier: pendingSubscriptionUpdate?.product_tier,
          billing_interval: pendingSubscriptionUpdate?.billing_interval,
          preview_if_upgrade: false,
        });

        if (data) {
          if (data.subscription) {
            setSubscription(data.subscription);
            if (data.subscription.unpaid_invoice_url) {
              setUnpaidDialogOpen(true);
            }
          }
          if (data.credit_account) {
            setCreditAccount(data.credit_account);
          }
          if (data.redirect) {
            window.location.href = data.redirect;
          }

          setPreviewInvoiceDialogOpen(false);
          setPreviewInvoice(null);
          setPendingSubscriptionUpdate(null);
        }
      }
    } finally {
    }
  };

  return (
    <>
      <Tabs
        value={tabValue || undefined}
        className="w-full"
        onValueChange={(value) => value && setTabValue(value as SubscriptionBillingIntervalEnum)}
      >
        <TabsList variant="pills" className="w-[300px] mx-auto mb-4">
          <TabsTrigger
            value={SubscriptionBillingIntervalEnum.yearly}
            className="pr-1 pl-3"
            disabled={loading}
          >
            Annually
            <span className="ml-1 text-[#A25AD9] bg-[rgba(162,90,217,0.12)] footnote rounded-full px-2 py-[3px]">
              2 months free
            </span>
          </TabsTrigger>
          <TabsTrigger value={SubscriptionBillingIntervalEnum.monthly} disabled={loading}>
            Monthly
          </TabsTrigger>
        </TabsList>
        <TabsContent
          key={SubscriptionBillingIntervalEnum.yearly}
          value={SubscriptionBillingIntervalEnum.yearly}
        >
          <AnnualMonthlyPlan
            signedIn={signedIn}
            type={SubscriptionBillingIntervalEnum.yearly}
            onUpgrade={handleOnPreviewOrUpgrade}
          />
        </TabsContent>
        <TabsContent
          key={SubscriptionBillingIntervalEnum.monthly}
          value={SubscriptionBillingIntervalEnum.monthly}
        >
          <AnnualMonthlyPlan
            signedIn={signedIn}
            type={SubscriptionBillingIntervalEnum.monthly}
            onUpgrade={handleOnPreviewOrUpgrade}
          />
        </TabsContent>
      </Tabs>
      <p className="body text-caption-fg mt-10 text-center">
        Prices exclude all taxes, levies and duties.
      </p>
      <UnpaidDialog
        open={unpaidDialogOpen}
        onOpenChange={setUnpaidDialogOpen}
        onConfirm={() => {
          if (subscription?.unpaid_invoice_url) {
            window.location.href = subscription.unpaid_invoice_url;
          }
        }}
      />
      <PreviewInvoiceDialog
        previewInvoice={previewInvoice}
        open={previewInvoiceDialogOpen}
        onOpenChange={setPreviewInvoiceDialogOpen}
        onConfirm={handleOnUpgrade}
      />
    </>
  );
};
