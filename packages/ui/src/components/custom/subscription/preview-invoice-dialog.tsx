import { PreviewInvoiceDto } from '@repo/api/generated-client/snake-case/index';
import { cn } from '@repo/ui/lib/utils';
import { upperFirst } from 'lodash-es';
import { useState } from 'react';
import { Button } from '../../ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../../ui/dialog';
import { Separator } from '../../ui/separator';

interface PreviewInvoiceDialogProps {
  previewInvoice: PreviewInvoiceDto | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => Promise<void>;
}

export const PreviewInvoiceDialog: React.FC<PreviewInvoiceDialogProps> = ({
  previewInvoice,
  open,
  onOpenChange,
  onConfirm,
}) => {
  const [loading, setLoading] = useState(false);

  const handleConfirm = async () => {
    setLoading(true);
    await onConfirm();
    setLoading(false);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="gap-0 max-w-[480px] p-6 rounded-3xl">
        <DialogHeader>
          <DialogTitle className="flex items-center title mb-6">Confirm changes</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          {previewInvoice?.lines?.map((line, index) => {
            const isCredit = line.amount < 0;
            return (
              <div key={index} className="space-y-1">
                <div className="flex justify-between items-center body-strong">
                  <span>{line.title}</span>
                  <span className={cn(isCredit && 'text-green-600')}>
                    {isCredit
                      ? `-$${Math.abs(line.amount / 100).toFixed(2)}`
                      : `$${(line.amount / 100).toFixed(2)}`}
                  </span>
                </div>
                <div className="text-secondary text-xs">{line.description}</div>
              </div>
            );
          })}
          <Separator />
          {previewInvoice?.discount && previewInvoice.discount > 0 ? (
            <>
              <div className="flex justify-between items-center body-strong">
                <span>Subtotal</span>
                <span>${((previewInvoice?.subtotal || 0) / 100).toFixed(2)}</span>
              </div>
              <div className="flex justify-between items-center body-strong">
                <span>Discount</span>
                <span className="text-green-600">
                  -${(previewInvoice.discount / 100).toFixed(2)}
                </span>
              </div>
            </>
          ) : null}
          <div className="flex justify-between items-center body-strong">
            <span>Total due today</span>
            <span>${((previewInvoice?.total || 0) / 100).toFixed(2)}</span>
          </div>
          {previewInvoice?.default_payment_method && (
            <>
              <Separator />
              <div className="flex justify-between items-center body-strong">
                <span>Payment method</span>
                <span className="body text-secondary">
                  {previewInvoice.default_payment_method.type === 'card'
                    ? `${upperFirst(previewInvoice.default_payment_method.card.brand)}*${previewInvoice.default_payment_method.card.last4}`
                    : previewInvoice.default_payment_method.type}
                </span>
              </div>
            </>
          )}
          <div className="flex justify-end">
            <Button onClick={handleConfirm} loading={loading}>
              Pay now
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
