import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@repo/ui/components/ui/dialog';
import { Table, TableBody, TableHead, TableHeader, TableRow } from '@repo/ui/components/ui/table';

interface AddonCreditsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const AddonCreditsDialog: React.FC<AddonCreditsDialogProps> = ({ open, onOpenChange }) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="gap-0 max-w-[480px] p-6 rounded-3xl">
        <DialogHeader>
          <DialogTitle className="flex items-center title mb-6">Add-on credits history</DialogTitle>
        </DialogHeader>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="text-caption-fg body w-[200px] p-0 h-9">Reason</TableHead>
              <TableHead className="text-caption-fg body p-0 h-9">Date</TableHead>
              <TableHead className="text-caption-fg body text-right p-0 h-9">Amount</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow>
              {/* <TableCell className="font-medium">INV001</TableCell>
              <TableCell>Paid</TableCell>
              <TableCell>Credit Card</TableCell>
              <TableCell className="text-right">$250.00</TableCell> */}
            </TableRow>
          </TableBody>
        </Table>
      </DialogContent>
    </Dialog>
  );
};
