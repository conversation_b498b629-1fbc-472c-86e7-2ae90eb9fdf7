.pricingTier {
  font-size: 24px;
  font-style: normal;
  font-weight: 500;
  line-height: 28px;
}

.title {
  font-size: 36px;
  font-style: normal;
  font-weight: 800;
  line-height: 40px;
}

.currency {
  font-size: 20px;
  font-style: normal;
  font-weight: 274;
  line-height: 32px;
}

.unit {
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
}

/* .ym-pricing-dot-item {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
} */

.popular {
  border-radius: 100px;
  background: rgba(162, 90, 217, 0.12);
  text-align: center;
  font-size: 12px;
  font-style: normal;
  font-weight: 590;
  line-height: 18px;
}

/* .ym-pricing-pro-yearly {
  background: linear-gradient(
    80deg,
    #ffaf40 -21.49%,
    #d194ec 18.44%,
    #9a8fea 61.08%,
    #65b4ff 107.78%
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
} */

.proYearly<PERSON>hina {
  font-size: 24px;
  font-style: normal;
  font-weight: 510;
  line-height: 32px;
  text-decoration-line: line-through;
}
