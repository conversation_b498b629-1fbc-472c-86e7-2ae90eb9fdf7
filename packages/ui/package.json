{"name": "@repo/ui", "version": "0.0.0", "private": true, "license": "MIT", "files": ["src"], "exports": {".": "./src/index.ts", "./globals.css": "./src/styles/globals.css", "./components/*": "./src/components/*", "./lib/*": "./src/lib/*.ts", "./lib/posthog/*": "./src/lib/posthog/*", "./hooks/*": "./src/hooks/*.ts"}, "scripts": {"build": "echo '✅ @repo/ui: No build needed - TypeScript source files consumed directly'", "shadcn:add": "pnpm dlx shadcn@latest add", "typecheck": "tsc --noEmit", "lint": "biome check . --diagnostic-level=error", "lint:fix": "biome check . --write --diagnostic-level=error", "format": "biome format . --write", "format:check": "biome format ."}, "dependencies": {"@infinite-canvas-tutorial/ecs": "0.0.1-alpha.39", "@infinite-canvas-tutorial/webcomponents": "0.0.1-alpha.39", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.7", "@repo/api": "workspace:*", "@repo/common": "workspace:*", "@repo/editor-common": "workspace:*", "@youmindinc/jsbridge": "catalog:", "@youmindinc/youcommon": "catalog:", "antd": "catalog:", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "copy-image-clipboard": "^2.1.2", "copy-to-clipboard": "^3.3.3", "date-fns": "^4.1.0", "franc-min": "catalog:", "howler": "catalog:", "i18next": "^25.3.2", "iso-639-3": "catalog:", "jotai": "catalog:", "katex": "catalog:", "lodash-es": "^4.17.21", "lucide-react": "catalog:", "mermaid": "catalog:", "next-themes": "^0.4.6", "posthog-js": "catalog:", "react": "catalog:", "react-blurhash": "^0.3.0", "react-day-picker": "^9.8.1", "react-dom": "catalog:", "react-dropzone": "^14.3.8", "react-hook-form": "catalog:", "react-i18next": "^15.6.0", "react-loader-spinner": "^6.1.6", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "sonner": "^2.0.6", "tailwind-merge": "^2.6.0", "unist-util-visit": "^5.0.0", "use-debounce": "^10.0.5", "usehooks-ts": "catalog:", "vaul": "^1.1.2"}, "devDependencies": {"@types/howler": "catalog:", "@types/lodash-es": "^4.17.12", "@types/react": "catalog:", "@types/react-dom": "catalog:", "@types/react-syntax-highlighter": "^15.5.13", "tailwindcss": "catalog:", "tailwindcss-animate": "^1.0.7", "typescript": "catalog:"}}