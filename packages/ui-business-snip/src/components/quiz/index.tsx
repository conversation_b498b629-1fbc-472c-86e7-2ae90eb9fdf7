import { SimpleLoading } from '@repo/ui/components/custom/simple-loading';
import { Button } from '@repo/ui/components/ui/button';
import { RotateCcw, Send, Sparkles, Trophy } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { QuizQuestionComponent } from './QuizQuestion';
import { QuizState } from './types';
import { parseQuizContent } from './utils/parseQuizContent';

interface Props {
  quizContent: string;
  title?: string;
}

export const Quiz = ({ quizContent, title = 'Quiz Challenge' }: Props) => {
  const [quizState, setQuizState] = useState<QuizState>({
    questions: [],
    currentQuestionIndex: 0,
    isSubmitted: false,
    score: 0,
    totalQuestions: 0,
  });

  const [animatedScore, setAnimatedScore] = useState(0);
  const [showCelebration, setShowCelebration] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // 分数递增动画效果
  useEffect(() => {
    if (quizState.isSubmitted && quizState.score > 0) {
      setAnimatedScore(0);
      setShowCelebration(true);

      const duration = 1500; // 1.5秒动画
      const steps = quizState.score * 10; // 分数 * 10 个步骤，让动画更流畅
      const increment = quizState.score / steps;
      const stepTime = duration / steps;

      let currentStep = 0;
      const timer = setInterval(() => {
        currentStep++;
        const newScore = Math.min(increment * currentStep, quizState.score);
        setAnimatedScore(Math.floor(newScore));

        if (currentStep >= steps) {
          clearInterval(timer);
          setAnimatedScore(quizState.score);
          // 3秒后隐藏庆祝效果
          setTimeout(() => setShowCelebration(false), 3000);
        }
      }, stepTime);

      return () => clearInterval(timer);
    }
  }, [quizState.isSubmitted, quizState.score]);

  // 解析测验内容
  useEffect(() => {
    if (quizContent) {
      const questions = parseQuizContent(quizContent);
      setQuizState({
        questions,
        currentQuestionIndex: 0,
        isSubmitted: false,
        score: 0,
        totalQuestions: questions.length,
      });
    }
  }, [quizContent]);

  // 处理答案变化
  const handleAnswerChange = (questionId: string, answer: number) => {
    if (quizState.isSubmitted) return;

    setQuizState((prev) => ({
      ...prev,
      questions: prev.questions.map((q) =>
        q.id === questionId ? { ...q, userAnswer: answer } : q,
      ),
    }));
  };

  // 提交答案
  const handleSubmit = () => {
    let score = 0;
    const updatedQuestions = quizState.questions.map((q) => {
      const isCorrect = q.userAnswer === q.answer;
      if (isCorrect) score += 1;

      return {
        ...q,
        isCorrect,
        showResult: true,
      };
    });

    setQuizState((prev) => ({
      ...prev,
      questions: updatedQuestions,
      isSubmitted: true,
      score,
    }));

    // 滚动到最底部 - 使用requestAnimationFrame确保DOM更新后滚动
    requestAnimationFrame(() => {
      containerRef.current?.scrollTo({
        top: containerRef.current?.scrollHeight,
        behavior: 'smooth',
      });
    });
  };

  // 重新开始
  const handleRestart = () => {
    setQuizState((prev) => ({
      ...prev,
      questions: prev.questions.map((q) => ({
        ...q,
        userAnswer: undefined,
        isCorrect: undefined,
        showResult: false,
      })),
      isSubmitted: false,
      score: 0,
    }));
  };

  // 检查是否所有题目都已回答
  const allQuestionsAnswered =
    quizState.questions.length > 0 && quizState.questions.every((q) => q.userAnswer !== undefined);

  // 计算通过率
  const passRate =
    quizState.totalQuestions > 0
      ? Math.round((quizState.score / quizState.totalQuestions) * 100)
      : 0;

  if (!quizContent || quizState.questions.length === 0) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <SimpleLoading />
      </div>
    );
  }

  return (
    <div className="h-full select-text max-w-[26em] mx-auto" ref={containerRef}>
      {/* 顶部标题区域 */}
      <div className="border-b py-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{title}</h1>
            <p className="mt-2 text-gray-600">
              Total Questions:{' '}
              <span className="font-semibold text-blue-600">{quizState.totalQuestions}</span>
            </p>
          </div>

          {/* 分数显示 */}
          {quizState.isSubmitted && (
            <div className="ml-3 text-center">
              <div className="flex items-center justify-center space-x-2 rounded-lg bg-white p-4 shadow-md">
                <Trophy
                  className={`h-6 w-6 ${passRate >= 70 ? 'text-yellow-500' : 'text-gray-400'}`}
                />
                <div>
                  <div className="text-2xl font-bold text-gray-900">
                    {quizState.score}/{quizState.totalQuestions}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="py-6">
        {/* 操作按钮栏 */}
        <div className="mb-6 flex items-center justify-between rounded-lg bg-gray-50 p-4">
          <div className="flex items-center space-x-4">
            {quizState.isSubmitted ? (
              <div className="text-sm text-gray-600">
                Score:{' '}
                <span className="font-medium text-blue-600">
                  {quizState.score}/{quizState.totalQuestions}
                </span>
              </div>
            ) : (
              <div className="text-sm text-gray-600">
                <span className="font-medium">
                  {quizState.questions.filter((q) => q.userAnswer !== undefined).length}
                </span>{' '}
                / {quizState.totalQuestions} answered
              </div>
            )}
          </div>

          {quizState.isSubmitted && (
            <Button
              onClick={handleRestart}
              variant="outline"
              size="sm"
              className="flex items-center space-x-1"
            >
              <RotateCcw className="h-4 w-4" />
              <span>Try Again</span>
            </Button>
          )}
        </div>

        {/* 题目列表 */}
        <div className="space-y-6">
          {quizState.questions.map((question, index) => (
            <QuizQuestionComponent
              key={question.id}
              question={question}
              index={index}
              onAnswerChange={handleAnswerChange}
              disabled={quizState.isSubmitted}
            />
          ))}
        </div>

        {/* 提交按钮 */}
        {!quizState.isSubmitted && quizState.questions.length > 0 && (
          <div className="flex justify-center py-6">
            <Button
              onClick={handleSubmit}
              disabled={!allQuestionsAnswered}
              size="lg"
              className="flex items-center space-x-2 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 disabled:opacity-50"
            >
              <Send className="h-4 w-4" />
              <span>Submit Answers</span>
            </Button>
          </div>
        )}

        {/* 结果总结 */}
        {quizState.isSubmitted && (
          <div
            className="relative mt-8 overflow-hidden rounded-xl border bg-gradient-to-r from-blue-50 to-indigo-50 p-6"
            style={{
              animation: 'slideInScale 0.8s cubic-bezier(0.34, 1.56, 0.64, 1) forwards',
            }}
          >
            {/* 庆祝粒子效果 */}
            {showCelebration &&
              passRate >= 70 &&
              [...Array(12)].map((_, i) => (
                <div
                  key={i}
                  className="absolute"
                  style={{
                    left: `${20 + i * 7}%`,
                    top: `${10 + (i % 3) * 20}%`,
                    animation: `sparkle 2s ease-in-out ${i * 0.1}s infinite`,
                  }}
                >
                  <Sparkles className="h-3 w-3 text-yellow-400" />
                </div>
              ))}

            <div className="relative z-10 text-center">
              <div
                className="mb-4"
                style={{
                  animation: 'bounceIn 1s ease-out 0.3s both',
                }}
              >
                <h3 className="mb-2 text-xl font-semibold text-gray-900">Quiz Complete! 🎉</h3>
              </div>

              <div
                className="mb-4"
                style={{
                  animation: 'fadeInUp 0.8s ease-out 0.6s both',
                }}
              >
                <p className="text-gray-600">
                  You scored{' '}
                  <span
                    className="text-2xl font-bold text-blue-600"
                    style={{
                      animation: 'pulseGlow 0.5s ease-in-out infinite alternate',
                    }}
                  >
                    {animatedScore}
                  </span>{' '}
                  out of <span className="font-medium">{quizState.totalQuestions}</span> questions
                  correctly.
                </p>
              </div>

              <div
                className="inline-flex items-center rounded-full px-4 py-2 text-sm font-medium"
                style={{
                  animation: 'bounceInScale 1.2s cubic-bezier(0.68, -0.55, 0.265, 1.55) 0.9s both',
                }}
              >
                {passRate >= 90 ? (
                  <span className="rounded-full bg-green-100 px-3 py-1 text-green-800 shadow-lg">
                    <span className="inline-block animate-bounce">🏆</span> Excellent! (90%+)
                  </span>
                ) : passRate >= 70 ? (
                  <span className="rounded-full bg-blue-100 px-3 py-1 text-blue-800 shadow-lg">
                    <span className="inline-block animate-bounce">👍</span> Good job! (70%+)
                  </span>
                ) : passRate >= 50 ? (
                  <span className="rounded-full bg-yellow-100 px-3 py-1 text-yellow-800 shadow-lg">
                    <span className="inline-block animate-bounce">📚</span> Keep learning! (50%+)
                  </span>
                ) : (
                  <span className="rounded-full bg-red-100 px-3 py-1 text-red-800 shadow-lg">
                    <span className="inline-block animate-bounce">💪</span> Try again! (below 50%)
                  </span>
                )}
              </div>
            </div>

            {/* CSS 动画样式 */}
            <style>{`
              @keyframes slideInScale {
                0% {
                  opacity: 0;
                  transform: translateY(30px) scale(0.9);
                }
                100% {
                  opacity: 1;
                  transform: translateY(0) scale(1);
                }
              }

              @keyframes bounceIn {
                0% {
                  opacity: 0;
                  transform: scale(0.3) translateY(-20px);
                }
                50% {
                  opacity: 1;
                  transform: scale(1.05) translateY(-10px);
                }
                70% {
                  transform: scale(0.95) translateY(-5px);
                }
                100% {
                  opacity: 1;
                  transform: scale(1) translateY(0);
                }
              }

              @keyframes fadeInUp {
                0% {
                  opacity: 0;
                  transform: translateY(20px);
                }
                100% {
                  opacity: 1;
                  transform: translateY(0);
                }
              }

              @keyframes bounceInScale {
                0% {
                  opacity: 0;
                  transform: scale(0);
                }
                50% {
                  opacity: 1;
                  transform: scale(1.2);
                }
                75% {
                  transform: scale(0.9);
                }
                100% {
                  opacity: 1;
                  transform: scale(1);
                }
              }

              @keyframes pulseGlow {
                0% {
                  transform: scale(1);
                  text-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
                }
                100% {
                  transform: scale(1.05);
                  text-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
                }
              }

              @keyframes sparkle {
                0%,
                100% {
                  opacity: 0;
                  transform: scale(0) rotate(0deg);
                }
                50% {
                  opacity: 1;
                  transform: scale(1) rotate(180deg);
                }
              }
            `}</style>
          </div>
        )}
      </div>
    </div>
  );
};
