import { cn } from '@repo/ui/lib/utils';
import { Check, X } from 'lucide-react';
import { MultipleChoiceQuestion, QuizQuestion, TrueFalseQuestion } from './types';

interface Props {
  question: QuizQuestion;
  index: number;
  onAnswerChange: (questionId: string, answer: number) => void;
  disabled?: boolean;
}

export const QuizQuestionComponent = ({
  question,
  index,
  onAnswerChange,
  disabled = false,
}: Props) => {
  const handleOptionClick = (optionIndex: number) => {
    if (disabled) return;
    onAnswerChange(question.id, optionIndex);
  };

  const renderTrueFalseQuestion = (q: TrueFalseQuestion) => {
    return (
      <div className="space-y-3">
        <div className="grid grid-cols-2 gap-3">
          {/* 真 选项 */}
          <button
            onClick={() => handleOptionClick(1)}
            disabled={disabled}
            className={cn(
              'flex items-center justify-center rounded-lg border-2 px-4 py-3 text-sm font-medium transition-all duration-200',
              'hover:shadow-md',
              q.userAnswer === 1
                ? 'border-blue-500 bg-blue-50 text-blue-700'
                : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300',
              disabled && 'cursor-not-allowed opacity-60',
              q.showResult &&
                q.userAnswer === 1 &&
                (q.answer === 1
                  ? 'border-green-500 bg-green-50 text-green-700'
                  : 'border-red-500 bg-red-50 text-red-700'),
            )}
          >
            <span className="mr-2">True</span>
            {q.showResult &&
              q.userAnswer === 1 &&
              (q.answer === 1 ? (
                <Check className="h-4 w-4 text-green-600" />
              ) : (
                <X className="h-4 w-4 text-red-600" />
              ))}
          </button>

          {/* 假 选项 */}
          <button
            onClick={() => handleOptionClick(0)}
            disabled={disabled}
            className={cn(
              'flex items-center justify-center rounded-lg border-2 px-4 py-3 text-sm font-medium transition-all duration-200',
              'hover:shadow-md',
              q.userAnswer === 0
                ? 'border-blue-500 bg-blue-50 text-blue-700'
                : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300',
              disabled && 'cursor-not-allowed opacity-60',
              q.showResult &&
                q.userAnswer === 0 &&
                (q.answer === 0
                  ? 'border-green-500 bg-green-50 text-green-700'
                  : 'border-red-500 bg-red-50 text-red-700'),
            )}
          >
            <span className="mr-2">False</span>
            {q.showResult &&
              q.userAnswer === 0 &&
              (q.answer === 0 ? (
                <Check className="h-4 w-4 text-green-600" />
              ) : (
                <X className="h-4 w-4 text-red-600" />
              ))}
          </button>
        </div>

        {/* 显示正确答案 */}
        {q.showResult && q.userAnswer !== q.answer && (
          <div className="mt-3 rounded-lg border border-green-200 bg-green-50 p-3">
            <div className="flex items-center text-sm text-green-700">
              <Check className="mr-2 h-4 w-4 text-green-600" />
              Correct answer: {q.answer === 1 ? 'True' : 'False'}
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderMultipleChoiceQuestion = (q: MultipleChoiceQuestion) => {
    return (
      <div className="space-y-3">
        {q.options.map((option, optionIndex) => {
          const isSelected = q.userAnswer === optionIndex + 1;
          const isCorrect = q.answer === optionIndex + 1;
          const showCorrect = q.showResult && isCorrect;
          const showIncorrect = q.showResult && isSelected && !isCorrect;

          return (
            <button
              key={optionIndex}
              onClick={() => handleOptionClick(optionIndex + 1)}
              disabled={disabled}
              className={cn(
                'flex w-full items-center justify-between rounded-lg border-2 px-4 py-3 text-left text-sm transition-all duration-200',
                'hover:shadow-md',
                isSelected
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300',
                disabled && 'cursor-not-allowed opacity-60',
                showCorrect && 'border-green-500 bg-green-50 text-green-700',
                showIncorrect && 'border-red-500 bg-red-50 text-red-700',
              )}
            >
              <div className="flex items-center">
                <span className="mr-3 flex h-6 w-6 items-center justify-center rounded-full bg-gray-100 text-xs font-medium text-gray-600">
                  {String.fromCharCode(65 + optionIndex)}
                </span>
                <span className="font-medium">{option}</span>
              </div>

              {q.showResult && (
                <div className="flex items-center">
                  {isCorrect && <Check className="h-4 w-4 text-green-600" />}
                  {showIncorrect && <X className="h-4 w-4 text-red-600" />}
                </div>
              )}
            </button>
          );
        })}

        {/* 显示正确答案，如果用户答错了 */}
        {q.showResult && q.userAnswer !== q.answer && (
          <div className="mt-3 rounded-lg border border-green-200 bg-green-50 p-3">
            <div className="flex items-center text-sm text-green-700">
              <Check className="mr-2 h-4 w-4 text-green-600" />
              Correct answer: {String.fromCharCode(64 + q.answer)} - {q.options[q.answer - 1]}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="rounded-xl border border-gray-200 bg-white p-6 shadow-sm transition-all duration-200 hover:shadow-md">
      {/* 题目标题 */}
      <div className="mb-4">
        <div className="mb-2 flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">Question {index + 1}</h3>
          {question.showResult && (
            <div
              className={cn(
                'flex items-center rounded-full px-2 py-1 text-xs font-medium',
                question.isCorrect ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700',
              )}
            >
              {question.isCorrect ? (
                <>
                  <Check className="mr-1 h-3 w-3" />
                  Correct
                </>
              ) : (
                <>
                  <X className="mr-1 h-3 w-3" />
                  Incorrect
                </>
              )}
            </div>
          )}
        </div>
        <p className="leading-relaxed text-gray-700">{question.title}</p>
      </div>

      {/* 题目选项 */}
      {question.type === 'true-false'
        ? renderTrueFalseQuestion(question)
        : renderMultipleChoiceQuestion(question)}
    </div>
  );
};
