import { MultipleChoiceQuestion, QuizQuestion, TrueFalseQuestion } from '../types';

export function parseQuizContent(content: string): QuizQuestion[] {
  const questions: QuizQuestion[] = [];
  const lines = content.split('\n').filter((line) => line.trim());

  let currentTitle = '';
  let currentOptions: string[] = [];
  let questionIndex = 0;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]?.trim();
    if (!line) {
      continue;
    }

    // 检测一级标题（题干）
    if (line.startsWith('# ')) {
      // 如果有之前的题目，先处理之前的题目
      if (currentTitle) {
        processCurrentQuestion();
      }

      currentTitle = line.substring(2).trim();
      currentOptions = [];
      continue;
    }

    // 检测二级标题（选项）
    if (line.startsWith('## ')) {
      const option = line.substring(3).trim();
      currentOptions.push(option);
      continue;
    }

    // 检测三级标题（答案）
    if (line.startsWith('### ') && currentTitle) {
      const answer = parseInt(line.substring(4).trim());
      if (!Number.isNaN(answer)) {
        createQuestion(currentTitle, currentOptions, answer);
        currentTitle = '';
        currentOptions = [];
      }
    }
  }

  // 处理最后一个题目
  if (currentTitle) {
    processCurrentQuestion();
  }

  function processCurrentQuestion() {
    // 这个函数用于处理当前正在构建的题目
    // 但是如果没有答案，我们不添加到questions中
  }

  function createQuestion(title: string, options: string[], answer: number) {
    questionIndex++;
    const id = `question_${questionIndex}`;

    if (options.length === 0) {
      // 判断题
      const question: TrueFalseQuestion = {
        id,
        title,
        type: 'true-false',
        answer,
      };
      questions.push(question);
    } else {
      // 单选题
      const question: MultipleChoiceQuestion = {
        id,
        title,
        type: 'multiple-choice',
        options,
        answer,
      };
      questions.push(question);
    }
  }

  return questions;
}

// 检测是否有新的完整题目
export function hasNewCompleteQuestion(content: string, lastParsedLength: number): boolean {
  const newContent = content.substring(lastParsedLength);

  // 检查是否有新的三级标题（答案）
  const answerPattern = /### \d+/;
  return answerPattern.test(newContent);
}

// 解析流式内容，只返回新的完整题目
export function parseStreamQuizContent(
  fullContent: string,
  existingQuestions: QuizQuestion[],
): QuizQuestion[] {
  const allQuestions = parseQuizContent(fullContent);

  // 返回新增的题目
  return allQuestions.slice(existingQuestions.length);
}
