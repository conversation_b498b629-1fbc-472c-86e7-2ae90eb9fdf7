export interface QuizQuestionBase {
  id: string;
  title: string;
  type: 'true-false' | 'multiple-choice';
  answer: number;
  userAnswer?: number;
  isCorrect?: boolean;
  showResult?: boolean;
}

export interface TrueFalseQuestion extends QuizQuestionBase {
  type: 'true-false';
  // 0 = false, 1 = true
}

export interface MultipleChoiceQuestion extends QuizQuestionBase {
  type: 'multiple-choice';
  options: string[];
  // answer is 1-based index
}

export type QuizQuestion = TrueFalseQuestion | MultipleChoiceQuestion;

export interface QuizState {
  questions: QuizQuestion[];
  currentQuestionIndex: number;
  isSubmitted: boolean;
  score: number;
  totalQuestions: number;
}
