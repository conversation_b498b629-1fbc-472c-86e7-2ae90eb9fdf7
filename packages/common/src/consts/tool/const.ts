export enum TOOL_TYPES {
  DIAGRAM_GENERATE = 'diagram_generate',
  IMAGE_GENERATE = 'image_generate',
  GOOGLE_SEARCH = 'google_search',
  LIBRARY_SEARCH = 'library_search',
  EDIT_THOUGHT = 'edit_thought',
  ORGANIZE_DIRECTORY_STRUCTURE = 'organize_directory_structure',
  CREATE_SNIP_BY_URL = 'create_snip_by_url',
  CREATE_BOARD = 'create_board',
  AUDIO_GENERATE = 'audio_generate',
  CREATE_PLAN = 'create_plan',
  UPDATE_PLAN = 'update_plan',
  BOARD_SEARCH = 'board_search',
  YOUTUBE_FACTORY = 'youtube_factory',
  PREFETCH_URLS = 'prefetch_urls',
}

export const ChatExplicitTools: TOOL_TYPES[] = [
  TOOL_TYPES.IMAGE_GENERATE,
  TOOL_TYPES.DIAGRAM_GENERATE,
  TOOL_TYPES.GOOGLE_SEARCH,
  TOOL_TYPES.EDIT_THOUGHT,
  // TOOL_TYPES.AUDIO_GENERATE,
];

export enum ImageGenerateToolSize {
  SQUARE = 'square',
  PORTRAIT = 'portrait',
  LANDSCAPE = 'landscape',
}
export enum ImageGenerateToolStyle {
  GHIBILI = 'ghibili',
  PIXAR = 'pixar',
  CARTOON = 'cartoon',
  PIXEL = 'pixel',
}

export enum ImageGenerateToolQuality {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
}
